/**
 * P1P2方塊大亂鬥 - 認證API處理 (MongoDB版本)
 */

const crypto = require('crypto');
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const nodemailer = require('nodemailer');
const { ObjectId } = require('mongodb');
const database = require('./database');
const DiscordOAuth = require('./discord-oauth');

// 載入環境變數
require('dotenv').config();

// Discord OAuth實例
const discordOAuth = new DiscordOAuth();

// 郵件配置
const emailTransporter = nodemailer.createTransport({
    host: process.env.SMTP_HOST || 'smtp.gmail.com',
    port: process.env.SMTP_PORT || 587,
    secure: false,
    auth: {
        user: process.env.SMTP_USER || '<EMAIL>',
        pass: process.env.SMTP_PASS || 'your-app-password'
    }
});

// 生成JWT Token
function generateJWT(user) {
    const payload = {
        userId: user._id,  // 使用userId而不是id
        id: user._id,      // 保持向後兼容
        email: user.email,
        username: user.username,
        verified: user.verified
    };

    return jwt.sign(payload, process.env.JWT_SECRET || 'default-secret', {
        expiresIn: '7d'
    });
}

// 驗證JWT Token
function verifyJWT(token) {
    try {
        const decoded = jwt.verify(token, process.env.JWT_SECRET || 'default-secret');
        console.log('🔍 JWT解碼結果:', {
            userId: decoded.userId,
            id: decoded.id,
            username: decoded.username,
            email: decoded.email
        });
        return decoded;
    } catch (error) {
        console.error('❌ JWT驗證失敗:', error.message);
        return null;
    }
}

// 生成驗證碼
function generateVerificationCode() {
    return Math.floor(100000 + Math.random() * 900000).toString();
}

// 發送驗證郵件
async function sendVerificationEmail(email, code) {
    try {
        const mailOptions = {
            from: process.env.FROM_EMAIL || '<EMAIL>',
            to: email,
            subject: 'P1P2方塊大亂鬥 - 郵件驗證',
            html: `
                <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
                    <h2 style="color: #333;">P1P2方塊大亂鬥</h2>
                    <h3 style="color: #666;">郵件驗證</h3>
                    <p>您的驗證碼是：</p>
                    <div style="background: #f5f5f5; padding: 20px; text-align: center; font-size: 24px; font-weight: bold; color: #333; margin: 20px 0;">
                        ${code}
                    </div>
                    <p>此驗證碼將在5分鐘後過期。</p>
                    <p>如果您沒有註冊P1P2方塊大亂鬥帳戶，請忽略此郵件。</p>
                </div>
            `
        };

        await emailTransporter.sendMail(mailOptions);
        console.log(`📧 驗證郵件已發送到: ${email}`);
        return true;
    } catch (error) {
        console.error('❌ 發送驗證郵件失敗:', error.message);
        return false;
    }
}

// 認證API路由處理
function setupAuthRoutes(app) {
    // 用戶註冊
    app.post('/api/auth/register', async (req, res) => {
        try {
            const { email, password, username } = req.body;

            if (!email || !password || !username) {
                res.writeHead(400, { 'Content-Type': 'application/json' });
                res.end(JSON.stringify({
                    success: false,
                    message: '請填寫所有必填欄位'
                }));
                return;
            }

            // 連接資料庫
            await database.connect();
            const users = database.getUsersCollection();

            // 檢查用戶是否已存在
            const existingUser = await users.findOne({ email });
            if (existingUser) {
                res.writeHead(400, { 'Content-Type': 'application/json' });
                res.end(JSON.stringify({
                    success: false,
                    message: '此郵箱已被註冊'
                }));
                return;
            }

            // 加密密碼
            const hashedPassword = await bcrypt.hash(password, 12);

            // 生成驗證碼
            const verificationCode = generateVerificationCode();
            const codeExpiry = new Date(Date.now() + 5 * 60 * 1000); // 5分鐘後過期

            // 創建用戶（包含經濟系統初始化）
            const newUser = {
                email,
                username,
                password: hashedPassword,
                verified: false,
                createdAt: new Date(),
                updatedAt: new Date(),
                lastActiveAt: new Date(),
                avatar: '',
                discord: null,
                twitch: null,
                // 經濟系統初始化
                level: 1,
                experience: 0,
                coins: 1000,        // 新用戶獲得1000金幣
                diamonds: 50,       // 新用戶獲得50鑽石
                // 遊戲統計初始化
                wins: 0,
                losses: 0,
                totalGames: 0,
                // 排名系統初始化
                rating: 1000,
                tier: 'bronze',
                // 成就系統初始化
                achievements: [],
                unlockedElements: ['fire', 'water', 'earth', 'air'], // 基礎元素
                // 每日任務和獎勵
                dailyRewards: {
                    lastClaimDate: null,
                    consecutiveDays: 0
                }
            };

            const result = await users.insertOne(newUser);

            // 儲存驗證碼
            const verificationCodes = database.getVerificationCodesCollection();
            await verificationCodes.insertOne({
                email,
                code: verificationCode,
                expiresAt: codeExpiry,
                createdAt: new Date()
            });

            // 發送驗證郵件
            await sendVerificationEmail(email, verificationCode);

            res.writeHead(200, { 'Content-Type': 'application/json' });
            res.end(JSON.stringify({
                success: true,
                message: '註冊成功，請檢查您的郵箱進行驗證',
                requiresVerification: true
            }));

        } catch (error) {
            console.error('❌ 註冊失敗:', error.message);
            res.writeHead(500, { 'Content-Type': 'application/json' });
            res.end(JSON.stringify({
                success: false,
                message: '註冊失敗，請稍後再試'
            }));
        }
    });

    // 用戶登入
    app.post('/api/auth/login', async (req, res) => {
        try {
            const { email, password } = req.body;

            if (!email || !password) {
                res.writeHead(400, { 'Content-Type': 'application/json' });
                res.end(JSON.stringify({
                    success: false,
                    message: '請填寫郵箱和密碼'
                }));
                return;
            }

            // 連接資料庫
            await database.connect();
            const users = database.getUsersCollection();

            // 查找用戶
            const user = await users.findOne({ email });
            if (!user) {
                res.writeHead(400, { 'Content-Type': 'application/json' });
                res.end(JSON.stringify({
                    success: false,
                    message: '郵箱或密碼錯誤'
                }));
                return;
            }

            // 驗證密碼
            const isValidPassword = await bcrypt.compare(password, user.password);
            if (!isValidPassword) {
                res.writeHead(400, { 'Content-Type': 'application/json' });
                res.end(JSON.stringify({
                    success: false,
                    message: '郵箱或密碼錯誤'
                }));
                return;
            }

            // 檢查是否已驗證
            if (!user.verified) {
                res.writeHead(200, { 'Content-Type': 'application/json' });
                res.end(JSON.stringify({
                    success: true,
                    requiresVerification: true,
                    message: '請先驗證您的郵箱'
                }));
                return;
            }

            // 生成JWT
            const token = generateJWT(user);

            // 更新最後登入時間
            await users.updateOne(
                { _id: user._id },
                { $set: { lastLoginAt: new Date() } }
            );

            res.writeHead(200, { 'Content-Type': 'application/json' });
            res.end(JSON.stringify({
                success: true,
                message: '登入成功',
                user: {
                    id: user._id,
                    email: user.email,
                    username: user.username,
                    verified: user.verified,
                    avatar: user.avatar || '',
                    discord_avatar: user.discord?.avatar || user.discord_avatar || '',
                    twitch_avatar: user.twitch?.avatar || user.twitch_avatar || ''
                },
                token
            }));

        } catch (error) {
            console.error('❌ 登入失敗:', error.message);
            res.writeHead(500, { 'Content-Type': 'application/json' });
            res.end(JSON.stringify({
                success: false,
                message: '登入失敗，請稍後再試'
            }));
        }
    });

    // 郵件驗證
    app.post('/api/auth/verify-email', async (req, res) => {
        try {
            const { email, code } = req.body;

            if (!email || !code) {
                res.writeHead(400, { 'Content-Type': 'application/json' });
                res.end(JSON.stringify({
                    success: false,
                    message: '請提供郵箱和驗證碼'
                }));
                return;
            }

            // 連接資料庫
            await database.connect();
            const verificationCodes = database.getVerificationCodesCollection();
            const users = database.getUsersCollection();

            // 查找驗證碼
            const verificationRecord = await verificationCodes.findOne({
                email,
                code,
                expiresAt: { $gt: new Date() }
            });

            if (!verificationRecord) {
                res.writeHead(400, { 'Content-Type': 'application/json' });
                res.end(JSON.stringify({
                    success: false,
                    message: '驗證碼無效或已過期'
                }));
                return;
            }

            // 更新用戶驗證狀態
            const user = await users.findOneAndUpdate(
                { email },
                {
                    $set: {
                        verified: true,
                        verifiedAt: new Date(),
                        updatedAt: new Date()
                    }
                },
                { returnDocument: 'after' }
            );

            if (!user.value) {
                res.writeHead(400, { 'Content-Type': 'application/json' });
                res.end(JSON.stringify({
                    success: false,
                    message: '用戶不存在'
                }));
                return;
            }

            // 刪除已使用的驗證碼
            await verificationCodes.deleteOne({ _id: verificationRecord._id });

            // 生成JWT
            const token = generateJWT(user.value);

            res.writeHead(200, { 'Content-Type': 'application/json' });
            res.end(JSON.stringify({
                success: true,
                message: '郵箱驗證成功',
                user: {
                    id: user.value._id,
                    email: user.value.email,
                    username: user.value.username,
                    verified: user.value.verified
                },
                token
            }));

        } catch (error) {
            console.error('❌ 郵件驗證失敗:', error.message);
            res.writeHead(500, { 'Content-Type': 'application/json' });
            res.end(JSON.stringify({
                success: false,
                message: '驗證失敗，請稍後再試'
            }));
        }
    });

    // Discord OAuth授權
    app.get('/api/auth/discord', async (req, res) => {
        try {
            // 檢查Discord配置
            if (!process.env.DISCORD_CLIENT_ID || process.env.DISCORD_CLIENT_ID === 'your_discord_client_id_here') {
                console.error('❌ Discord Client ID未設置或仍為預設值');
                res.writeHead(302, { 'Location': '/auth?error=discord_not_configured' });
                res.end();
                return;
            }

            const authUrl = discordOAuth.getAuthUrl();
            console.log('🔗 Discord授權URL:', authUrl);

            res.writeHead(302, { 'Location': authUrl });
            res.end();
        } catch (error) {
            console.error('❌ Discord授權失敗:', error.message);
            res.writeHead(302, { 'Location': '/auth?error=discord_auth_failed' });
            res.end();
        }
    });

    // Discord OAuth回調
    app.get('/auth/discord/callback', async (req, res) => {
        try {
            const url = new URL(req.url, `http://${req.headers.host}`);
            const code = url.searchParams.get('code');
            const error = url.searchParams.get('error');

            if (error) {
                console.error('❌ Discord授權被拒絕:', error);
                res.writeHead(302, { 'Location': '/auth?error=discord_denied' });
                res.end();
                return;
            }

            if (!code) {
                console.error('❌ 未收到Discord授權碼');
                res.writeHead(302, { 'Location': '/auth?error=no_code' });
                res.end();
                return;
            }

            console.log('📝 收到Discord授權碼，正在交換access token...');

            // 第1步：交換授權碼獲取access token
            const tokenData = await discordOAuth.exchangeCode(code);
            console.log('🔑 獲取到access token');

            // 第2步：使用access token獲取用戶信息
            const userInfo = await discordOAuth.getUserInfo(tokenData.access_token);
            console.log('👤 Discord用戶信息:', {
                id: userInfo.id,
                username: userInfo.username,
                email: userInfo.email,
                verified: userInfo.verified
            });

            // 連接資料庫
            await database.connect();
            const users = database.getUsersCollection();

            // 檢查是否已有此Discord用戶
            const existingUser = await users.findOne({ 'discord.id': userInfo.id });

            if (existingUser) {
                // 現有用戶，直接登入
                console.log('👤 現有用戶登入');

                // 更新 Discord 頭像
                let discordAvatarUrl = null;
                if (userInfo.avatar) {
                    // userInfo.avatar 已經是完整的URL，不需要重新構建
                    discordAvatarUrl = userInfo.avatar;

                    // 如果需要指定尺寸，只需要添加參數
                    if (!discordAvatarUrl.includes('?size=')) {
                        discordAvatarUrl += '?size=256';
                    }

                    // 更新用戶頭像
                    await users.updateOne(
                        { _id: existingUser._id },
                        {
                            $set: {
                                avatar: discordAvatarUrl,
                                'discord.avatar': discordAvatarUrl,
                                updatedAt: new Date()
                            }
                        }
                    );
                }

                const token = generateJWT(existingUser);

                // 重新獲取更新後的用戶數據
                const updatedUser = await users.findOne({ _id: existingUser._id });

                // 準備用戶數據
                const userData = {
                    id: updatedUser._id,
                    email: updatedUser.email,
                    username: updatedUser.username,
                    verified: updatedUser.verified,
                    avatar: updatedUser.avatar || '',
                    discord_avatar: updatedUser.discord?.avatar || updatedUser.discord_avatar || '',
                    twitch_avatar: updatedUser.twitch?.avatar || updatedUser.twitch_avatar || ''
                };

                console.log('🖼️ 返回的用戶數據:', userData);

                res.writeHead(200, { 'Content-Type': 'text/html' });
                res.end(`
                    <html>
                        <head><title>登入成功</title></head>
                        <body>
                            <script>
                                localStorage.setItem('user', '${JSON.stringify(userData)}');
                                localStorage.setItem('token', '${token}');
                                window.location.href = '/game';
                            </script>
                        </body>
                    </html>
                `);
            } else {
                // 新用戶，需要設置遊戲密碼
                console.log('🆕 新用戶，需要設置密碼');
                // 構建 Discord 頭像 URL
                let discordAvatarUrl = null;
                if (userInfo.avatar) {
                    // userInfo.avatar 已經是完整的URL，不需要重新構建
                    discordAvatarUrl = userInfo.avatar;

                    // 如果需要指定尺寸，只需要添加參數
                    if (!discordAvatarUrl.includes('?size=')) {
                        discordAvatarUrl += '?size=256';
                    }
                }

                const tempUserData = {
                    platform: 'Discord',
                    id: userInfo.id,
                    username: userInfo.discriminator && userInfo.discriminator !== '0'
                        ? userInfo.username + '#' + userInfo.discriminator
                        : userInfo.username,
                    email: userInfo.email,
                    avatar: discordAvatarUrl,
                    discord_avatar: discordAvatarUrl, // 專門的 Discord 頭像欄位
                    verified: userInfo.verified
                };

                res.writeHead(200, { 'Content-Type': 'text/html' });
                res.end(`
                    <html>
                        <head><title>設置遊戲密碼</title></head>
                        <body>
                            <script>
                                sessionStorage.setItem('socialAuthData', '${JSON.stringify(tempUserData)}');
                                window.location.href = '/auth?step=set-password&platform=discord';
                            </script>
                        </body>
                    </html>
                `);
            }

        } catch (error) {
            console.error('❌ Discord回調處理失敗:', error.message);
            res.writeHead(302, { 'Location': '/auth?error=callback_failed' });
            res.end();
        }
    });

    // 設置社交登入密碼
    app.post('/api/auth/set-social-password', async (req, res) => {
        try {
            const { platform, id, username, email, avatar, verified, gamePassword } = req.body;

            if (!gamePassword || gamePassword.length < 6) {
                res.writeHead(400, { 'Content-Type': 'application/json' });
                res.end(JSON.stringify({
                    success: false,
                    message: '密碼長度至少6個字符'
                }));
                return;
            }

            // 連接資料庫
            await database.connect();
            const users = database.getUsersCollection();

            // 檢查郵箱是否已被使用
            if (email) {
                const existingEmailUser = await users.findOne({ email });
                if (existingEmailUser) {
                    res.writeHead(400, { 'Content-Type': 'application/json' });
                    res.end(JSON.stringify({
                        success: false,
                        message: '此郵箱已被其他帳戶使用'
                    }));
                    return;
                }
            }

            // 加密密碼
            const hashedPassword = await bcrypt.hash(gamePassword, 12);

            // 創建新用戶（包含經濟系統初始化）
            const newUser = {
                email: email || null,
                username,
                password: hashedPassword,
                verified: verified || false,
                avatar: avatar, // 主頭像
                discord: platform === 'Discord' ? {
                    id,
                    username,
                    avatar
                } : undefined,
                twitch: platform === 'Twitch' ? {
                    id,
                    username,
                    avatar
                } : undefined,
                createdAt: new Date(),
                updatedAt: new Date(),
                lastActiveAt: new Date(),
                // 經濟系統初始化
                level: 1,
                experience: 0,
                coins: 1000,        // 新用戶獲得1000金幣
                diamonds: 50,       // 新用戶獲得50鑽石
                // 遊戲統計初始化
                wins: 0,
                losses: 0,
                totalGames: 0,
                // 排名系統初始化
                rating: 1000,
                tier: 'bronze',
                // 成就系統初始化
                achievements: [],
                unlockedElements: ['fire', 'water', 'earth', 'air'], // 基礎元素
                // 每日任務和獎勵
                dailyRewards: {
                    lastClaimDate: null,
                    consecutiveDays: 0
                }
            };

            const result = await users.insertOne(newUser);
            const createdUser = await users.findOne({ _id: result.insertedId });

            // 生成JWT
            const token = generateJWT(createdUser);

            console.log(`✅ ${platform}用戶創建成功:`, username);

            res.writeHead(200, { 'Content-Type': 'application/json' });
            res.end(JSON.stringify({
                success: true,
                message: '帳戶創建成功',
                user: {
                    id: createdUser._id,
                    email: createdUser.email,
                    username: createdUser.username,
                    verified: createdUser.verified,
                    avatar: createdUser.avatar || '',
                    discord_avatar: createdUser.discord?.avatar || '',
                    twitch_avatar: createdUser.twitch?.avatar || ''
                },
                token
            }));

        } catch (error) {
            console.error('❌ 設置社交登入密碼失敗:', error.message);
            res.writeHead(500, { 'Content-Type': 'application/json' });
            res.end(JSON.stringify({
                success: false,
                message: '設置失敗，請稍後再試'
            }));
        }
    });

    // 資料庫狀態檢查
    app.get('/api/auth/db-status', async (req, res) => {
        try {
            await database.connect();
            const stats = await database.getStats();

            res.writeHead(200, { 'Content-Type': 'application/json' });
            res.end(JSON.stringify({
                success: true,
                database: stats
            }));
        } catch (error) {
            console.error('❌ 資料庫狀態檢查失敗:', error.message);
            res.writeHead(500, { 'Content-Type': 'application/json' });
            res.end(JSON.stringify({
                success: false,
                message: '資料庫連接失敗'
            }));
        }
    });

    // 獲取當前用戶信息
    app.get('/api/auth/me', async (req, res) => {
        try {
            const authHeader = req.headers.authorization;
            if (!authHeader || !authHeader.startsWith('Bearer ')) {
                res.writeHead(401, { 'Content-Type': 'application/json' });
                res.end(JSON.stringify({
                    success: false,
                    message: '未提供有效的認證token'
                }));
                return;
            }

            const token = authHeader.substring(7);
            const decoded = verifyJWT(token);

            if (!decoded) {
                res.writeHead(401, { 'Content-Type': 'application/json' });
                res.end(JSON.stringify({
                    success: false,
                    message: 'token無效或已過期'
                }));
                return;
            }

            // 連接資料庫
            await database.connect();
            const users = database.getUsersCollection();

            // 獲取用戶最新數據（兼容舊版JWT）
            const userId = decoded.userId || decoded.id;
            console.log('🔍 查找用戶ID:', userId);

            const user = await users.findOne({ _id: new ObjectId(userId) });
            if (!user) {
                res.writeHead(404, { 'Content-Type': 'application/json' });
                res.end(JSON.stringify({
                    success: false,
                    message: '用戶不存在'
                }));
                return;
            }

            console.log('🖼️ 返回用戶最新數據:', {
                id: user._id,
                username: user.username,
                avatar: user.avatar,
                discord_avatar: user.discord?.avatar || user.discord_avatar,
                twitch_avatar: user.twitch?.avatar || user.twitch_avatar
            });

            // 返回用戶數據（包含最新頭像和經濟數據）
            res.writeHead(200, { 'Content-Type': 'application/json' });
            res.end(JSON.stringify({
                success: true,
                user: {
                    id: user._id,
                    email: user.email,
                    username: user.username,
                    verified: user.verified,
                    avatar: user.avatar || '',
                    discord_avatar: user.discord?.avatar || user.discord_avatar || '',
                    twitch_avatar: user.twitch?.avatar || user.twitch_avatar || '',
                    // 經濟數據
                    level: user.level || 1,
                    experience: user.experience || 0,
                    coins: user.coins || 1000,
                    diamonds: user.diamonds || 0,
                    // 遊戲統計
                    wins: user.wins || 0,
                    losses: user.losses || 0,
                    totalGames: (user.wins || 0) + (user.losses || 0),
                    winRate: (user.wins || 0) > 0 ? ((user.wins || 0) / ((user.wins || 0) + (user.losses || 0)) * 100).toFixed(1) : '0.0',
                    // 排名數據
                    rating: user.rating || 1000,
                    tier: user.tier || 'bronze',
                    // 時間數據
                    createdAt: user.createdAt,
                    lastActiveAt: user.lastActiveAt || new Date()
                }
            }));

        } catch (error) {
            console.error('❌ 獲取用戶信息失敗:', error.message);
            res.writeHead(500, { 'Content-Type': 'application/json' });
            res.end(JSON.stringify({
                success: false,
                message: '獲取用戶信息失敗'
            }));
        }
    });

    // 獲取當前用戶信息 (修復 /api/auth/me 端點)
    app.get('/api/auth/me', async (req, res) => {
        try {
            const authHeader = req.headers.authorization;
            if (!authHeader || !authHeader.startsWith('Bearer ')) {
                res.writeHead(401, { 'Content-Type': 'application/json' });
                res.end(JSON.stringify({
                    success: false,
                    message: '未提供認證令牌'
                }));
                return;
            }

            const token = authHeader.substring(7);
            const decoded = jwt.verify(token, JWT_SECRET);

            // 連接資料庫
            await database.connect();
            const users = database.getUsersCollection();

            // 獲取用戶最新數據（兼容舊版JWT）
            const userId = decoded.userId || decoded.id;
            console.log('🔍 查找用戶ID:', userId);

            const user = await users.findOne({ _id: new ObjectId(userId) });
            if (!user) {
                res.writeHead(404, { 'Content-Type': 'application/json' });
                res.end(JSON.stringify({
                    success: false,
                    message: '用戶不存在'
                }));
                return;
            }

            console.log('🖼️ 返回用戶最新數據:', {
                id: user._id,
                username: user.username,
                avatar: user.avatar,
                discord_avatar: user.discord?.avatar || user.discord_avatar,
                twitch_avatar: user.twitch?.avatar || user.twitch_avatar
            });

            // 返回用戶數據（包含最新頭像和經濟數據）
            res.writeHead(200, { 'Content-Type': 'application/json' });
            res.end(JSON.stringify({
                success: true,
                user: {
                    id: user._id,
                    email: user.email,
                    username: user.username,
                    verified: user.verified,
                    avatar: user.avatar || '',
                    discord_avatar: user.discord?.avatar || user.discord_avatar || '',
                    twitch_avatar: user.twitch?.avatar || user.twitch_avatar || '',
                    // 經濟數據
                    level: user.level || 1,
                    experience: user.experience || 0,
                    coins: user.coins || 1000,
                    diamonds: user.diamonds || 50,
                    // 遊戲統計
                    wins: user.wins || 0,
                    losses: user.losses || 0,
                    totalGames: (user.wins || 0) + (user.losses || 0),
                    winRate: (user.wins || 0) > 0 ? ((user.wins || 0) / ((user.wins || 0) + (user.losses || 0)) * 100).toFixed(1) : '0.0',
                    // 排名數據
                    rating: user.rating || 1000,
                    tier: user.tier || 'bronze',
                    // 時間數據
                    createdAt: user.createdAt,
                    lastActiveAt: user.lastActiveAt || new Date()
                }
            }));

        } catch (error) {
            console.error('❌ 獲取用戶信息失敗:', error.message);
            res.writeHead(500, { 'Content-Type': 'application/json' });
            res.end(JSON.stringify({
                success: false,
                message: '獲取用戶信息失敗'
            }));
        }
    });
}

module.exports = { setupAuthRoutes };
