/**
 * 自動緩存破壞系統
 * 在頁面載入時自動為CSS和JS文件添加版本號
 */

(function() {
    'use strict';

    // 檢測是否為開發模式
    const isDevelopment = window.location.hostname === 'localhost' ||
                         window.location.hostname === '127.0.0.1' ||
                         window.location.hostname.includes('p1p2blockgame.com');

    // 只在開發模式或指定域名下啟用緩存破壞
    if (!isDevelopment) {
        console.log('🚀 生產模式，跳過緩存破壞');
        return;
    }

    // 生成基於時間戳的版本號
    const VERSION = Date.now().toString(36);

    console.log('🛠️ 緩存破壞系統已載入，版本號:', VERSION);

    // 更新CSS文件
    function updateCSSFiles() {
        const cssLinks = document.querySelectorAll('link[rel="stylesheet"][href*=".css"]');
        let updatedCount = 0;

        cssLinks.forEach(link => {
            const href = link.getAttribute('href');
            // 只處理本地CSS文件，跳過外部CDN
            if (href && !href.startsWith('http') && href.includes('.css')) {
                const newHref = href.split('?')[0] + '?v=' + VERSION;
                if (href !== newHref) {
                    link.setAttribute('href', newHref);
                    updatedCount++;
                    console.log('🔄 更新CSS:', href, '->', newHref);
                }
            }
        });

        return updatedCount;
    }

    // 更新JS文件
    function updateJSFiles() {
        const jsScripts = document.querySelectorAll('script[src*=".js"]');
        let updatedCount = 0;

        jsScripts.forEach(script => {
            const src = script.getAttribute('src');
            // 只處理本地JS文件，跳過外部CDN和當前腳本
            if (src && !src.startsWith('http') && src.includes('.js') && !src.includes('cache-buster')) {
                const newSrc = src.split('?')[0] + '?v=' + VERSION;
                if (src !== newSrc) {
                    // 創建新的script標籤
                    const newScript = document.createElement('script');
                    newScript.src = newSrc;
                    newScript.async = script.async;
                    newScript.defer = script.defer;

                    // 複製其他屬性
                    Array.from(script.attributes).forEach(attr => {
                        if (attr.name !== 'src') {
                            newScript.setAttribute(attr.name, attr.value);
                        }
                    });

                    // 替換舊腳本
                    script.parentNode.insertBefore(newScript, script);
                    script.remove();

                    updatedCount++;
                    console.log('🔄 更新JS:', src, '->', newSrc);
                }
            }
        });

        return updatedCount;
    }

    // 強制刷新緩存
    function bustCache() {
        const cssCount = updateCSSFiles();
        const jsCount = updateJSFiles();

        console.log(`✅ 緩存破壞完成: ${cssCount} CSS文件, ${jsCount} JS文件`);

        // 設置無緩存頭部
        if ('serviceWorker' in navigator) {
            navigator.serviceWorker.getRegistrations().then(registrations => {
                registrations.forEach(registration => {
                    registration.unregister();
                    console.log('🗑️ 清除Service Worker緩存');
                });
            });
        }

        // 清除瀏覽器緩存
        if ('caches' in window) {
            caches.keys().then(names => {
                names.forEach(name => {
                    caches.delete(name);
                    console.log('🗑️ 清除緩存:', name);
                });
            });
        }
    }

    // 添加手動刷新功能
    window.bustCache = bustCache;
    window.forceRefresh = function() {
        console.log('🔄 強制刷新頁面...');
        bustCache();
        setTimeout(() => {
            window.location.reload(true);
        }, 100);
    };

    // 不自動執行緩存破壞，避免重複載入腳本
    // 只提供手動功能

    // 添加鍵盤快捷鍵 Ctrl+Shift+R 強制刷新
    document.addEventListener('keydown', function(e) {
        if (e.ctrlKey && e.shiftKey && e.key === 'R') {
            e.preventDefault();
            window.forceRefresh();
        }
    });

    console.log('💡 提示: 使用 bustCache() 手動破壞緩存，或按 Ctrl+Shift+R 強制刷新');

})();
