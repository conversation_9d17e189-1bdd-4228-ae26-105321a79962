/**
 * P1P2方塊大亂鬥 - 每日獎勵和成就系統API
 */

const jwt = require('jsonwebtoken');
const { ObjectId } = require('mongodb');
const database = require('./database');
const { DAILY_REWARDS, calculateLevel } = require('./user-economy-api');

const JWT_SECRET = process.env.JWT_SECRET || 'p1p2-game-secret-key-2024';

// 驗證JWT令牌
function verifyToken(req) {
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
        throw new Error('未提供認證令牌');
    }

    const token = authHeader.substring(7);
    const decoded = jwt.verify(token, JWT_SECRET);
    return decoded.userId || decoded.id;
}

// 檢查是否為同一天
function isSameDay(date1, date2) {
    if (!date1 || !date2) return false;
    const d1 = new Date(date1);
    const d2 = new Date(date2);
    return d1.getFullYear() === d2.getFullYear() &&
           d1.getMonth() === d2.getMonth() &&
           d1.getDate() === d2.getDate();
}

// 檢查是否為連續天數
function isConsecutiveDay(lastDate, currentDate) {
    if (!lastDate) return false;
    const last = new Date(lastDate);
    const current = new Date(currentDate);
    const diffTime = current - last;
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays === 1;
}

// 成就配置
const ACHIEVEMENTS = {
    // 遊戲相關成就
    'first_win': {
        name: '初次勝利',
        description: '贏得你的第一場遊戲',
        icon: '🏆',
        rewards: { coins: 500, diamonds: 10 },
        condition: (user) => (user.wins || 0) >= 1
    },
    'win_streak_5': {
        name: '連勝達人',
        description: '連續贏得5場遊戲',
        icon: '🔥',
        rewards: { coins: 1000, diamonds: 20 },
        condition: (user) => (user.currentWinStreak || 0) >= 5
    },
    'total_wins_10': {
        name: '勝利之路',
        description: '總共贏得10場遊戲',
        icon: '⭐',
        rewards: { coins: 800, diamonds: 15 },
        condition: (user) => (user.wins || 0) >= 10
    },
    'total_wins_50': {
        name: '戰場老兵',
        description: '總共贏得50場遊戲',
        icon: '🎖️',
        rewards: { coins: 2000, diamonds: 50 },
        condition: (user) => (user.wins || 0) >= 50
    },
    'total_wins_100': {
        name: '傳奇戰士',
        description: '總共贏得100場遊戲',
        icon: '👑',
        rewards: { coins: 5000, diamonds: 100 },
        condition: (user) => (user.wins || 0) >= 100
    },

    // 等級相關成就
    'level_10': {
        name: '新手畢業',
        description: '達到10級',
        icon: '📈',
        rewards: { coins: 1000, diamonds: 25 },
        condition: (user) => calculateLevel(user.experience || 0).level >= 10
    },
    'level_25': {
        name: '進階玩家',
        description: '達到25級',
        icon: '🚀',
        rewards: { coins: 2500, diamonds: 50 },
        condition: (user) => calculateLevel(user.experience || 0).level >= 25
    },
    'level_50': {
        name: '資深玩家',
        description: '達到50級',
        icon: '💎',
        rewards: { coins: 5000, diamonds: 100 },
        condition: (user) => calculateLevel(user.experience || 0).level >= 50
    },

    // 每日獎勵相關成就
    'daily_7': {
        name: '堅持不懈',
        description: '連續7天領取每日獎勵',
        icon: '📅',
        rewards: { coins: 1500, diamonds: 30 },
        condition: (user) => (user.dailyRewards?.consecutiveDays || 0) >= 7
    },
    'daily_30': {
        name: '月度勇士',
        description: '連續30天領取每日獎勵',
        icon: '🗓️',
        rewards: { coins: 5000, diamonds: 100 },
        condition: (user) => (user.dailyRewards?.consecutiveDays || 0) >= 30
    },

    // 經濟相關成就
    'rich_10k': {
        name: '小富翁',
        description: '擁有10,000金幣',
        icon: '💰',
        rewards: { coins: 0, diamonds: 20 },
        condition: (user) => (user.coins || 0) >= 10000
    },
    'rich_50k': {
        name: '大富翁',
        description: '擁有50,000金幣',
        icon: '💸',
        rewards: { coins: 0, diamonds: 50 },
        condition: (user) => (user.coins || 0) >= 50000
    }
};

// 設置每日獎勵和成就API路由
function setupDailyRewardsRoutes(app) {

    // 獲取每日獎勵狀態
    app.get('/api/user/daily-rewards', async (req, res) => {
        try {
            const userId = verifyToken(req);

            await database.connect();
            const users = database.getUsersCollection();

            const user = await users.findOne({ _id: new ObjectId(userId) });
            if (!user) {
                res.writeHead(404, { 'Content-Type': 'application/json' });
                res.end(JSON.stringify({
                    success: false,
                    message: '用戶不存在'
                }));
                return;
            }

            const dailyRewards = user.dailyRewards || {
                lastClaimDate: null,
                consecutiveDays: 0
            };

            const today = new Date();
            const canClaim = !dailyRewards.lastClaimDate || !isSameDay(dailyRewards.lastClaimDate, today);

            // 計算下一個獎勵
            const nextRewardDay = Math.min(dailyRewards.consecutiveDays, DAILY_REWARDS.length - 1);
            const nextReward = DAILY_REWARDS[nextRewardDay];

            res.writeHead(200, { 'Content-Type': 'application/json' });
            res.end(JSON.stringify({
                success: true,
                data: {
                    canClaim,
                    consecutiveDays: dailyRewards.consecutiveDays,
                    lastClaimDate: dailyRewards.lastClaimDate,
                    nextReward,
                    allRewards: DAILY_REWARDS
                }
            }));

        } catch (error) {
            console.error('❌ 獲取每日獎勵狀態失敗:', error.message);
            res.writeHead(500, { 'Content-Type': 'application/json' });
            res.end(JSON.stringify({
                success: false,
                message: '獲取狀態失敗'
            }));
        }
    });

    // 領取每日獎勵
    app.post('/api/user/claim-daily-reward', async (req, res) => {
        try {
            const userId = verifyToken(req);

            await database.connect();
            const users = database.getUsersCollection();

            const user = await users.findOne({ _id: new ObjectId(userId) });
            if (!user) {
                res.writeHead(404, { 'Content-Type': 'application/json' });
                res.end(JSON.stringify({
                    success: false,
                    message: '用戶不存在'
                }));
                return;
            }

            const dailyRewards = user.dailyRewards || {
                lastClaimDate: null,
                consecutiveDays: 0
            };

            const today = new Date();

            // 檢查是否已經領取過今天的獎勵
            if (dailyRewards.lastClaimDate && isSameDay(dailyRewards.lastClaimDate, today)) {
                res.writeHead(400, { 'Content-Type': 'application/json' });
                res.end(JSON.stringify({
                    success: false,
                    message: '今天已經領取過獎勵了'
                }));
                return;
            }

            // 計算連續天數
            let newConsecutiveDays;
            if (!dailyRewards.lastClaimDate) {
                // 第一次領取
                newConsecutiveDays = 1;
            } else if (isConsecutiveDay(dailyRewards.lastClaimDate, today)) {
                // 連續領取
                newConsecutiveDays = dailyRewards.consecutiveDays + 1;
            } else {
                // 中斷了，重新開始
                newConsecutiveDays = 1;
            }

            // 如果超過7天，重置為1（重新開始週期）
            if (newConsecutiveDays > 7) {
                newConsecutiveDays = 1;
            }

            // 獲取獎勵
            const rewardIndex = Math.min(newConsecutiveDays - 1, DAILY_REWARDS.length - 1);
            const reward = DAILY_REWARDS[rewardIndex];

            // 更新用戶數據
            const newCoins = (user.coins || 1000) + reward.coins;
            const newDiamonds = (user.diamonds || 50) + reward.diamonds;

            await users.updateOne(
                { _id: new ObjectId(userId) },
                {
                    $set: {
                        coins: newCoins,
                        diamonds: newDiamonds,
                        'dailyRewards.lastClaimDate': today,
                        'dailyRewards.consecutiveDays': newConsecutiveDays,
                        lastActiveAt: new Date()
                    }
                }
            );

            res.writeHead(200, { 'Content-Type': 'application/json' });
            res.end(JSON.stringify({
                success: true,
                data: {
                    reward,
                    consecutiveDays: newConsecutiveDays,
                    newCoins,
                    newDiamonds,
                    message: `第${newConsecutiveDays}天獎勵已領取！`
                }
            }));

        } catch (error) {
            console.error('❌ 領取每日獎勵失敗:', error.message);
            res.writeHead(500, { 'Content-Type': 'application/json' });
            res.end(JSON.stringify({
                success: false,
                message: '領取獎勵失敗'
            }));
        }
    });

    // 獲取成就列表
    app.get('/api/user/achievements', async (req, res) => {
        try {
            const userId = verifyToken(req);

            await database.connect();
            const users = database.getUsersCollection();

            const user = await users.findOne({ _id: new ObjectId(userId) });
            if (!user) {
                res.writeHead(404, { 'Content-Type': 'application/json' });
                res.end(JSON.stringify({
                    success: false,
                    message: '用戶不存在'
                }));
                return;
            }

            const userAchievements = user.achievements || [];
            const achievementList = [];

            // 檢查所有成就
            for (const [id, achievement] of Object.entries(ACHIEVEMENTS)) {
                const isUnlocked = userAchievements.includes(id);
                const canUnlock = !isUnlocked && achievement.condition(user);

                achievementList.push({
                    id,
                    name: achievement.name,
                    description: achievement.description,
                    icon: achievement.icon,
                    rewards: achievement.rewards,
                    isUnlocked,
                    canUnlock
                });
            }

            res.writeHead(200, { 'Content-Type': 'application/json' });
            res.end(JSON.stringify({
                success: true,
                data: {
                    achievements: achievementList,
                    totalAchievements: Object.keys(ACHIEVEMENTS).length,
                    unlockedCount: userAchievements.length
                }
            }));

        } catch (error) {
            console.error('❌ 獲取成就列表失敗:', error.message);
            res.writeHead(500, { 'Content-Type': 'application/json' });
            res.end(JSON.stringify({
                success: false,
                message: '獲取成就失敗'
            }));
        }
    });

    // 檢查並解鎖成就
    app.post('/api/user/check-achievements', async (req, res) => {
        try {
            const userId = verifyToken(req);

            await database.connect();
            const users = database.getUsersCollection();

            const user = await users.findOne({ _id: new ObjectId(userId) });
            if (!user) {
                res.writeHead(404, { 'Content-Type': 'application/json' });
                res.end(JSON.stringify({
                    success: false,
                    message: '用戶不存在'
                }));
                return;
            }

            const userAchievements = user.achievements || [];
            const newAchievements = [];
            let totalCoinsReward = 0;
            let totalDiamondsReward = 0;

            // 檢查所有成就
            for (const [id, achievement] of Object.entries(ACHIEVEMENTS)) {
                if (!userAchievements.includes(id) && achievement.condition(user)) {
                    newAchievements.push({
                        id,
                        name: achievement.name,
                        description: achievement.description,
                        icon: achievement.icon,
                        rewards: achievement.rewards
                    });

                    totalCoinsReward += achievement.rewards.coins || 0;
                    totalDiamondsReward += achievement.rewards.diamonds || 0;
                    userAchievements.push(id);
                }
            }

            // 如果有新成就，更新用戶數據
            if (newAchievements.length > 0) {
                const updateData = {
                    achievements: userAchievements,
                    lastActiveAt: new Date()
                };

                if (totalCoinsReward > 0) {
                    updateData.coins = (user.coins || 1000) + totalCoinsReward;
                }
                if (totalDiamondsReward > 0) {
                    updateData.diamonds = (user.diamonds || 50) + totalDiamondsReward;
                }

                await users.updateOne(
                    { _id: new ObjectId(userId) },
                    { $set: updateData }
                );
            }

            res.writeHead(200, { 'Content-Type': 'application/json' });
            res.end(JSON.stringify({
                success: true,
                data: {
                    newAchievements,
                    totalCoinsReward,
                    totalDiamondsReward,
                    message: newAchievements.length > 0 ?
                        `恭喜！解鎖了 ${newAchievements.length} 個新成就！` :
                        '暫無新成就解鎖'
                }
            }));

        } catch (error) {
            console.error('❌ 檢查成就失敗:', error.message);
            res.writeHead(500, { 'Content-Type': 'application/json' });
            res.end(JSON.stringify({
                success: false,
                message: '檢查成就失敗'
            }));
        }
    });
}

module.exports = {
    setupDailyRewardsRoutes,
    ACHIEVEMENTS,
    isSameDay,
    isConsecutiveDay
};
