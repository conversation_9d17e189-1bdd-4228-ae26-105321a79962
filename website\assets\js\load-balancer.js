/**
 * P1P2方塊大亂鬥 - 負載均衡器
 * 負責在多個服務器實例之間分配玩家和遊戲負載
 */

class LoadBalancer {
    constructor() {
        this.servers = new Map(); // 服務器實例列表
        this.currentServerIndex = 0;
        this.balancingStrategy = 'round_robin'; // round_robin, least_connections, weighted
        this.healthCheckInterval = 120000; // 2分鐘健康檢查
        this.failoverEnabled = true;

        this.metrics = {
            totalRequests: 0,
            successfulRequests: 0,
            failedRequests: 0,
            averageResponseTime: 0,
            lastBalanceTime: Date.now()
        };

        this.init();
    }

    init() {
        console.log('⚖️ 負載均衡器初始化...');

        // 註冊默認服務器
        this.registerDefaultServers();

        // 設置健康檢查
        this.setupHealthCheck();

        // 設置性能監控
        this.setupPerformanceMonitoring();

        console.log('✅ 負載均衡器已啟動');
    }

    // 註冊默認服務器
    registerDefaultServers() {
        // 主服務器
        this.registerServer({
            id: 'main-server',
            url: window.location.origin,
            wsUrl: `${window.location.protocol === 'https:' ? 'wss:' : 'ws:'}//${window.location.host}/ws`,
            weight: 10,
            maxConnections: 1000,
            region: 'primary'
        });

        // 如果有其他服務器配置，可以在這裡添加
        // this.registerServer({
        //     id: 'backup-server',
        //     url: 'https://backup.p1p2blockgame.com',
        //     wsUrl: 'wss://backup.p1p2blockgame.com/ws',
        //     weight: 5,
        //     maxConnections: 500,
        //     region: 'backup'
        // });
    }

    // 註冊服務器
    registerServer(serverConfig) {
        const server = {
            id: serverConfig.id,
            url: serverConfig.url,
            wsUrl: serverConfig.wsUrl,
            weight: serverConfig.weight || 1,
            maxConnections: serverConfig.maxConnections || 1000,
            region: serverConfig.region || 'default',
            status: 'unknown', // unknown, healthy, unhealthy, maintenance
            currentConnections: 0,
            totalRequests: 0,
            successfulRequests: 0,
            failedRequests: 0,
            averageResponseTime: 0,
            lastHealthCheck: 0,
            lastResponseTime: 0,
            registeredAt: Date.now()
        };

        this.servers.set(serverConfig.id, server);

        // 立即進行健康檢查
        this.checkServerHealth(serverConfig.id);

        console.log(`📡 註冊服務器: ${serverConfig.id} (${serverConfig.url})`);
        return server;
    }

    // 選擇最佳服務器
    selectBestServer(criteria = {}) {
        const availableServers = this.getAvailableServers(criteria);

        if (availableServers.length === 0) {
            throw new Error('沒有可用的服務器');
        }

        let selectedServer;

        switch (this.balancingStrategy) {
            case 'round_robin':
                selectedServer = this.selectByRoundRobin(availableServers);
                break;
            case 'least_connections':
                selectedServer = this.selectByLeastConnections(availableServers);
                break;
            case 'weighted':
                selectedServer = this.selectByWeight(availableServers);
                break;
            case 'response_time':
                selectedServer = this.selectByResponseTime(availableServers);
                break;
            default:
                selectedServer = availableServers[0];
        }

        this.recordServerSelection(selectedServer);
        return selectedServer;
    }

    // 獲取可用服務器
    getAvailableServers(criteria = {}) {
        return Array.from(this.servers.values()).filter(server => {
            // 基本健康檢查
            if (server.status !== 'healthy') return false;

            // 連接數檢查
            if (server.currentConnections >= server.maxConnections) return false;

            // 區域檢查
            if (criteria.region && server.region !== criteria.region) return false;

            // 最小權重檢查
            if (criteria.minWeight && server.weight < criteria.minWeight) return false;

            return true;
        });
    }

    // 輪詢選擇
    selectByRoundRobin(servers) {
        const server = servers[this.currentServerIndex % servers.length];
        this.currentServerIndex = (this.currentServerIndex + 1) % servers.length;
        return server;
    }

    // 最少連接選擇
    selectByLeastConnections(servers) {
        return servers.reduce((best, current) => {
            return current.currentConnections < best.currentConnections ? current : best;
        });
    }

    // 權重選擇
    selectByWeight(servers) {
        const totalWeight = servers.reduce((sum, server) => sum + server.weight, 0);
        let random = Math.random() * totalWeight;

        for (const server of servers) {
            random -= server.weight;
            if (random <= 0) {
                return server;
            }
        }

        return servers[0]; // 備用
    }

    // 響應時間選擇
    selectByResponseTime(servers) {
        return servers.reduce((best, current) => {
            return current.averageResponseTime < best.averageResponseTime ? current : best;
        });
    }

    // 記錄服務器選擇
    recordServerSelection(server) {
        server.totalRequests++;
        server.currentConnections++;
        this.metrics.totalRequests++;

        console.log(`🎯 選擇服務器: ${server.id} (連接數: ${server.currentConnections})`);
    }

    // 釋放服務器連接
    releaseServerConnection(serverId, success = true, responseTime = 0) {
        const server = this.servers.get(serverId);
        if (!server) return;

        server.currentConnections = Math.max(0, server.currentConnections - 1);

        if (success) {
            server.successfulRequests++;
            this.metrics.successfulRequests++;
        } else {
            server.failedRequests++;
            this.metrics.failedRequests++;
        }

        // 更新響應時間
        if (responseTime > 0) {
            server.lastResponseTime = responseTime;
            server.averageResponseTime = (server.averageResponseTime + responseTime) / 2;
        }
    }

    // 健康檢查
    setupHealthCheck() {
        setInterval(() => {
            this.performHealthChecks();
        }, this.healthCheckInterval);
    }

    // 執行健康檢查
    async performHealthChecks() {
        const healthCheckPromises = Array.from(this.servers.keys()).map(serverId =>
            this.checkServerHealth(serverId)
        );

        await Promise.allSettled(healthCheckPromises);
    }

    // 檢查單個服務器健康狀態
    async checkServerHealth(serverId) {
        const server = this.servers.get(serverId);
        if (!server) return;

        const startTime = Date.now();

        try {
            // 發送健康檢查請求
            const response = await fetch(`${server.url}/health`, {
                method: 'GET',
                timeout: 5000,
                headers: {
                    'User-Agent': 'P1P2-LoadBalancer/1.0'
                }
            });

            const responseTime = Date.now() - startTime;

            if (response.ok) {
                const healthData = await response.json();
                this.updateServerHealth(serverId, 'healthy', responseTime, healthData);
            } else {
                this.updateServerHealth(serverId, 'unhealthy', responseTime);
            }
        } catch (error) {
            const responseTime = Date.now() - startTime;
            this.updateServerHealth(serverId, 'unhealthy', responseTime, { error: error.message });
        }
    }

    // 更新服務器健康狀態
    updateServerHealth(serverId, status, responseTime, healthData = {}) {
        const server = this.servers.get(serverId);
        if (!server) return;

        const previousStatus = server.status;
        server.status = status;
        server.lastHealthCheck = Date.now();
        server.lastResponseTime = responseTime;

        // 更新平均響應時間
        if (server.averageResponseTime === 0) {
            server.averageResponseTime = responseTime;
        } else {
            server.averageResponseTime = (server.averageResponseTime * 0.8) + (responseTime * 0.2);
        }

        // 如果狀態改變，記錄日誌
        if (previousStatus !== status) {
            console.log(`🏥 服務器 ${serverId} 狀態變更: ${previousStatus} -> ${status}`);

            if (status === 'unhealthy' && this.failoverEnabled) {
                this.handleServerFailure(serverId);
            }
        }

        // 更新服務器特定的健康數據
        if (healthData.load) {
            server.currentConnections = healthData.load.connections || server.currentConnections;
        }
    }

    // 處理服務器故障
    handleServerFailure(serverId) {
        const server = this.servers.get(serverId);
        if (!server) return;

        console.warn(`🚨 服務器故障: ${serverId}`);

        // 通知系統管理員
        this.notifyServerFailure(serverId);

        // 如果只有一個服務器，嘗試恢復
        const healthyServers = this.getAvailableServers();
        if (healthyServers.length === 0) {
            console.error('🆘 所有服務器都不可用！');
            this.enableEmergencyMode();
        }
    }

    // 通知服務器故障
    notifyServerFailure(serverId) {
        // 這裡可以實現通知邏輯，比如發送郵件或推送通知
        console.error(`📧 通知管理員: 服務器 ${serverId} 故障`);
    }

    // 啟用緊急模式
    enableEmergencyMode() {
        console.error('🆘 啟用緊急模式');

        // 在緊急模式下，我們不創建新的服務器實例
        // 而是標記主服務器為健康狀態，允許繼續使用
        const mainServer = this.servers.get('main-server');
        if (mainServer) {
            console.log('🔄 緊急模式：強制主服務器為健康狀態');
            mainServer.status = 'healthy';
        }
    }

    // 性能監控
    setupPerformanceMonitoring() {
        setInterval(() => {
            this.updatePerformanceMetrics();
        }, 60000); // 每分鐘更新一次
    }

    // 更新性能指標
    updatePerformanceMetrics() {
        const totalRequests = this.metrics.totalRequests;
        const successRate = totalRequests > 0 ?
            (this.metrics.successfulRequests / totalRequests) * 100 : 100;

        // 計算平均響應時間
        const servers = Array.from(this.servers.values());
        const avgResponseTime = servers.reduce((sum, server) =>
            sum + server.averageResponseTime, 0) / servers.length;

        this.metrics.averageResponseTime = avgResponseTime;
        this.metrics.successRate = successRate;

        // 記錄性能日誌
        if (totalRequests > 0) {
            console.log(`📊 負載均衡性能: 成功率 ${successRate.toFixed(1)}%, 平均響應時間 ${avgResponseTime.toFixed(0)}ms`);
        }
    }

    // 獲取最佳WebSocket服務器
    getBestWebSocketServer(criteria = {}) {
        try {
            const server = this.selectBestServer(criteria);
            return {
                url: server.wsUrl,
                serverId: server.id,
                region: server.region
            };
        } catch (error) {
            console.error('❌ 無法獲取WebSocket服務器:', error);

            // 返回默認服務器
            return {
                url: `${window.location.protocol === 'https:' ? 'wss:' : 'ws:'}//${window.location.host}/ws`,
                serverId: 'default',
                region: 'default'
            };
        }
    }

    // 獲取最佳HTTP服務器
    getBestHttpServer(criteria = {}) {
        try {
            const server = this.selectBestServer(criteria);
            return {
                url: server.url,
                serverId: server.id,
                region: server.region
            };
        } catch (error) {
            console.error('❌ 無法獲取HTTP服務器:', error);

            // 返回默認服務器
            return {
                url: window.location.origin,
                serverId: 'default',
                region: 'default'
            };
        }
    }

    // 設置負載均衡策略
    setBalancingStrategy(strategy) {
        const validStrategies = ['round_robin', 'least_connections', 'weighted', 'response_time'];

        if (validStrategies.includes(strategy)) {
            this.balancingStrategy = strategy;
            console.log(`⚖️ 負載均衡策略已更改為: ${strategy}`);
        } else {
            console.error('❌ 無效的負載均衡策略:', strategy);
        }
    }

    // 獲取負載均衡統計
    getLoadBalancingStats() {
        const servers = Array.from(this.servers.values());

        return {
            strategy: this.balancingStrategy,
            totalServers: servers.length,
            healthyServers: servers.filter(s => s.status === 'healthy').length,
            totalConnections: servers.reduce((sum, s) => sum + s.currentConnections, 0),
            metrics: this.metrics,
            servers: servers.map(server => ({
                id: server.id,
                status: server.status,
                connections: server.currentConnections,
                maxConnections: server.maxConnections,
                responseTime: server.averageResponseTime,
                successRate: server.totalRequests > 0 ?
                    (server.successfulRequests / server.totalRequests) * 100 : 100,
                region: server.region
            }))
        };
    }

    // 手動故障轉移
    manualFailover(fromServerId, toServerId) {
        const fromServer = this.servers.get(fromServerId);
        const toServer = this.servers.get(toServerId);

        if (!fromServer || !toServer) {
            throw new Error('服務器不存在');
        }

        // 標記源服務器為維護狀態
        fromServer.status = 'maintenance';

        console.log(`🔄 手動故障轉移: ${fromServerId} -> ${toServerId}`);

        return {
            from: fromServerId,
            to: toServerId,
            timestamp: Date.now()
        };
    }

    // 移除服務器
    removeServer(serverId) {
        const server = this.servers.get(serverId);
        if (!server) return false;

        // 如果服務器有活躍連接，先標記為維護狀態
        if (server.currentConnections > 0) {
            server.status = 'maintenance';
            console.log(`⚠️ 服務器 ${serverId} 有活躍連接，標記為維護狀態`);
            return false;
        }

        this.servers.delete(serverId);
        console.log(`🗑️ 移除服務器: ${serverId}`);
        return true;
    }
}

// 創建全局實例
window.loadBalancer = new LoadBalancer();

// 添加全局調試命令
window.debugLoadBalancer = {
    stats: () => window.loadBalancer.getLoadBalancingStats(),
    servers: () => Array.from(window.loadBalancer.servers.values()),
    strategy: (newStrategy) => {
        if (newStrategy) {
            window.loadBalancer.setBalancingStrategy(newStrategy);
        }
        return window.loadBalancer.balancingStrategy;
    },
    health: () => window.loadBalancer.performHealthChecks(),
    best: () => window.loadBalancer.selectBestServer()
};

// 導出供其他模組使用
if (typeof module !== 'undefined' && module.exports) {
    module.exports = LoadBalancer;
}
