# P1P2方塊大亂鬥 - 個人資料彈出視窗系統

## 📋 概述

個人資料彈出視窗系統是P1P2方塊大亂鬥的全新用戶界面組件，提供了更現代化和用戶友好的個人資料查看體驗。用戶可以在不離開當前頁面的情況下查看完整的個人資料、遊戲統計、經濟狀況和成就進度。

## 🎯 設計理念

- **非侵入式體驗**：彈出視窗設計讓用戶無需離開當前頁面
- **響應式設計**：完美適配桌面、平板和手機設備
- **現代化界面**：使用毛玻璃效果和流暢動畫
- **完整功能**：包含所有原有個人資料頁面的功能

## 📁 文件結構

```
website/
├── profile-modal.js          # 彈出視窗JavaScript組件
├── profile-modal.css         # 彈出視窗樣式文件
├── profile.html             # 重定向頁面（自動打開彈出視窗）
├── profile-demo.html        # 演示頁面
└── game.html               # 已集成彈出視窗功能
```

## 🔧 核心組件

### 1. ProfileModal 類 (`profile-modal.js`)

主要的JavaScript類，負責：
- 創建和管理彈出視窗DOM結構
- 處理用戶交互事件
- 載入和顯示用戶資料
- API調用和錯誤處理

#### 主要方法：
- `init()` - 初始化彈出視窗
- `open()` - 打開彈出視窗並載入資料
- `close()` - 關閉彈出視窗
- `loadUserProfile()` - 載入用戶資料
- `claimDailyReward()` - 領取每日獎勵
- `checkAchievements()` - 檢查成就

### 2. 樣式系統 (`profile-modal.css`)

提供完整的視覺設計：
- 毛玻璃效果背景
- 流暢的開關動畫
- 響應式網格佈局
- 現代化的卡片設計
- 移動設備優化

### 3. 全局函數

- `openProfileModal()` - 全局函數，可在任何頁面調用
- `initProfileModal()` - 初始化函數

## 🎮 功能特色

### 📊 完整的用戶資料展示
- **基本信息**：用戶名、等級、頭像
- **經濟數據**：金幣、鑽石餘額
- **等級進度**：當前等級、經驗值、進度條
- **遊戲統計**：總場次、勝負記錄、勝率
- **排名信息**：評分、段位

### 🎁 互動功能
- **每日獎勵**：一鍵領取每日獎勵
- **成就檢查**：查看成就進度
- **實時更新**：操作後自動刷新資料

### 📱 用戶體驗
- **快速載入**：優化的載入動畫
- **錯誤處理**：友好的錯誤提示
- **鍵盤支持**：ESC鍵關閉
- **點擊外部關閉**：直觀的交互方式

## 🔗 API 集成

彈出視窗與以下API端點集成：

- `GET /api/user/economy` - 獲取用戶經濟數據
- `GET /api/user/achievements` - 獲取成就列表
- `GET /api/user/daily-rewards` - 獲取每日獎勵狀態
- `POST /api/user/claim-daily-reward` - 領取每日獎勵

## 🎨 視覺設計

### 色彩方案
- **主色調**：紫色漸變 (#667eea → #764ba2)
- **強調色**：橙色漸變 (#ff6b35 → #f7931e)
- **金幣色**：金色漸變 (#ffd700 → #ffed4e)
- **鑽石色**：藍色漸變 (#00d4ff → #0099cc)

### 動畫效果
- **開啟動畫**：淡入 + 縮放
- **關閉動畫**：淡出 + 縮放
- **懸停效果**：輕微上移 + 陰影
- **載入動畫**：旋轉載入器

## 📱 響應式設計

### 桌面版 (>768px)
- 雙列網格佈局
- 完整的統計卡片
- 橫向按鈕排列

### 平板版 (768px-480px)
- 單列網格佈局
- 調整字體大小
- 垂直按鈕排列

### 手機版 (<480px)
- 緊湊的佈局
- 優化的觸摸目標
- 簡化的統計顯示

## 🔧 使用方法

### 在現有頁面中集成

1. **引入CSS和JS文件**：
```html
<link rel="stylesheet" href="/profile-modal.css?v=economy">
<script src="/profile-modal.js?v=economy"></script>
```

2. **調用彈出視窗**：
```javascript
// 直接調用
openProfileModal();

// 或者在按鈕點擊事件中
<button onclick="openProfileModal()">個人資料</button>
```

### 自定義配置

可以通過修改 `ProfileModal` 類來自定義：
- API端點
- 樣式主題
- 動畫效果
- 錯誤處理

## 🚀 部署狀態

✅ **已完成**：
- 核心彈出視窗組件開發
- 樣式系統設計
- API集成
- 響應式適配
- 遊戲頁面集成
- 演示頁面創建

✅ **已部署**：
- 生產環境部署完成
- API端點正常工作
- 所有功能測試通過

## 🔮 未來計劃

- **成就詳情彈出視窗**：點擊成就顯示詳細信息
- **排行榜集成**：在彈出視窗中顯示排行榜
- **社交功能**：好友列表和聊天功能
- **主題自定義**：用戶可選擇不同的視覺主題

## 📞 技術支持

如需技術支持或有任何問題，請聯繫開發團隊。

---

**P1P2方塊大亂鬥開發團隊**  
*讓每一次戰鬥都充滿樂趣！* 🎮✨
