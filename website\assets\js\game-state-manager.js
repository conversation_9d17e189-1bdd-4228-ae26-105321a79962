/**
 * P1P2方塊大亂鬥 - 遊戲狀態管理器
 * 負責管理遊戲狀態、數據同步、狀態恢復
 */

class GameStateManager {
    constructor() {
        this.currentState = 'idle';
        this.stateHistory = [];
        this.gameData = {};
        this.syncQueue = [];
        this.isOnline = true;
        this.lastSyncTime = 0;
        this.syncInterval = 1000; // 1秒同步一次

        this.states = {
            'idle': '待機',
            'matching': '匹配中',
            'selecting': '選擇方塊',
            'battling': '戰鬥中',
            'finished': '遊戲結束',
            'error': '錯誤狀態'
        };

        this.init();
    }

    init() {
        console.log('🎮 遊戲狀態管理器初始化...');

        // 設置狀態變化監聽
        this.setupStateListeners();

        // 設置數據同步
        this.setupDataSync();

        // 設置自動保存
        this.setupAutoSave();

        // 恢復上次狀態
        this.restoreLastState();

        console.log('✅ 遊戲狀態管理器已啟動');
    }

    // 設置狀態監聽
    setupStateListeners() {
        // 監聽連接狀態變化
        window.addEventListener('connectionFallback', (event) => {
            this.handleConnectionLoss(event.detail.connection);
        });

        // 監聽頁面可見性變化
        document.addEventListener('visibilitychange', () => {
            if (document.hidden) {
                this.handlePageHidden();
            } else {
                this.handlePageVisible();
            }
        });

        // 監聽頁面卸載
        window.addEventListener('beforeunload', () => {
            this.saveCurrentState();
        });
    }

    // 設置數據同步
    setupDataSync() {
        setInterval(() => {
            if (this.isOnline && this.syncQueue.length > 0) {
                this.processSyncQueue();
            }
        }, this.syncInterval);
    }

    // 設置自動保存
    setupAutoSave() {
        setInterval(() => {
            this.saveCurrentState();
        }, 30000); // 每30秒自動保存
    }

    // 改變遊戲狀態
    changeState(newState, data = {}) {
        const oldState = this.currentState;

        if (!this.isValidStateTransition(oldState, newState)) {
            console.warn(`⚠️ 無效的狀態轉換: ${oldState} -> ${newState}`);
            return false;
        }

        // 記錄狀態歷史
        this.stateHistory.push({
            from: oldState,
            to: newState,
            timestamp: Date.now(),
            data: { ...data }
        });

        // 保持歷史記錄在合理範圍內
        if (this.stateHistory.length > 50) {
            this.stateHistory = this.stateHistory.slice(-25);
        }

        this.currentState = newState;
        this.gameData = { ...this.gameData, ...data };

        console.log(`🔄 狀態變更: ${this.states[oldState]} -> ${this.states[newState]}`);

        // 觸發狀態變更事件
        this.emitStateChange(oldState, newState, data);

        // 保存狀態
        this.saveCurrentState();

        return true;
    }

    // 檢查狀態轉換是否有效
    isValidStateTransition(from, to) {
        const validTransitions = {
            'idle': ['matching', 'error'],
            'matching': ['selecting', 'idle', 'error'],
            'selecting': ['battling', 'idle', 'error'],
            'battling': ['finished', 'idle', 'error'],
            'finished': ['idle', 'matching', 'error'],
            'error': ['idle']
        };

        return validTransitions[from]?.includes(to) || false;
    }

    // 觸發狀態變更事件
    emitStateChange(oldState, newState, data) {
        const event = new CustomEvent('gameStateChange', {
            detail: {
                oldState,
                newState,
                data,
                timestamp: Date.now()
            }
        });
        window.dispatchEvent(event);
    }

    // 獲取當前狀態
    getCurrentState() {
        return {
            state: this.currentState,
            data: this.gameData,
            timestamp: Date.now()
        };
    }

    // 獲取狀態歷史
    getStateHistory() {
        return [...this.stateHistory];
    }

    // 回滾到上一個狀態
    rollbackToPreviousState() {
        if (this.stateHistory.length < 2) {
            console.warn('⚠️ 沒有可回滾的狀態');
            return false;
        }

        const previousState = this.stateHistory[this.stateHistory.length - 2];
        console.log(`🔙 回滾到狀態: ${this.states[previousState.to]}`);

        this.currentState = previousState.to;
        this.gameData = { ...previousState.data };

        // 移除當前狀態記錄
        this.stateHistory.pop();

        this.emitStateChange(this.currentState, previousState.to, previousState.data);
        return true;
    }

    // 重置到初始狀態
    resetToInitialState() {
        console.log('🔄 重置到初始狀態');

        this.currentState = 'idle';
        this.gameData = {};
        this.stateHistory = [];
        this.syncQueue = [];

        this.emitStateChange('unknown', 'idle', {});
        this.saveCurrentState();
    }

    // 處理連接丟失
    handleConnectionLoss(connectionName) {
        console.log(`📡 連接丟失: ${connectionName}`);
        this.isOnline = false;

        // 如果在關鍵狀態，保存當前進度
        if (['selecting', 'battling'].includes(this.currentState)) {
            this.saveCurrentState();
            this.addToSyncQueue('connection_lost', {
                state: this.currentState,
                data: this.gameData,
                timestamp: Date.now()
            });
        }
    }

    // 處理頁面隱藏
    handlePageHidden() {
        console.log('👁️ 頁面隱藏，保存狀態');
        this.saveCurrentState();
    }

    // 處理頁面可見
    handlePageVisible() {
        console.log('👁️ 頁面可見，檢查狀態');
        this.checkStateConsistency();
    }

    // 檢查狀態一致性
    checkStateConsistency() {
        const savedState = this.loadSavedState();
        if (savedState && savedState.timestamp > this.lastSyncTime) {
            console.log('🔄 檢測到狀態不一致，恢復保存的狀態');
            this.restoreState(savedState);
        }
    }

    // 保存當前狀態
    saveCurrentState() {
        const stateData = {
            state: this.currentState,
            data: this.gameData,
            history: this.stateHistory.slice(-10), // 只保存最近10個狀態
            timestamp: Date.now()
        };

        try {
            localStorage.setItem('gameState', JSON.stringify(stateData));
            sessionStorage.setItem('gameState', JSON.stringify(stateData));
        } catch (error) {
            console.error('❌ 保存狀態失敗:', error);
        }
    }

    // 載入保存的狀態
    loadSavedState() {
        try {
            // 優先從sessionStorage載入
            let stateData = sessionStorage.getItem('gameState');
            if (!stateData) {
                stateData = localStorage.getItem('gameState');
            }

            return stateData ? JSON.parse(stateData) : null;
        } catch (error) {
            console.error('❌ 載入狀態失敗:', error);
            return null;
        }
    }

    // 恢復上次狀態
    restoreLastState() {
        const savedState = this.loadSavedState();
        if (savedState) {
            // 檢查狀態是否過期（超過1小時）
            const now = Date.now();
            if (now - savedState.timestamp < 3600000) { // 1小時
                console.log('🔄 恢復上次保存的狀態');
                this.restoreState(savedState);
            } else {
                console.log('⏰ 保存的狀態已過期，重置到初始狀態');
                this.resetToInitialState();
            }
        }
    }

    // 恢復狀態
    restoreState(stateData) {
        this.currentState = stateData.state || 'idle';
        this.gameData = stateData.data || {};
        this.stateHistory = stateData.history || [];
        this.lastSyncTime = stateData.timestamp || Date.now();

        console.log(`✅ 狀態已恢復: ${this.states[this.currentState]}`);
        this.emitStateChange('unknown', this.currentState, this.gameData);
    }

    // 添加到同步隊列
    addToSyncQueue(type, data) {
        this.syncQueue.push({
            type,
            data,
            timestamp: Date.now(),
            retries: 0
        });
    }

    // 處理同步隊列
    processSyncQueue() {
        if (this.syncQueue.length === 0) return;

        const item = this.syncQueue.shift();
        this.syncToServer(item).then(() => {
            console.log(`✅ 同步成功: ${item.type}`);
        }).catch((error) => {
            console.error(`❌ 同步失敗: ${item.type}`, error);

            // 重試機制
            if (item.retries < 3) {
                item.retries++;
                this.syncQueue.unshift(item); // 重新加入隊列前端
            }
        });
    }

    // 同步到服務器
    async syncToServer(item) {
        if (!this.isOnline) {
            throw new Error('離線狀態');
        }

        // 這裡實現實際的同步邏輯
        return fetch('/api/sync-state', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(item)
        });
    }

    // 獲取狀態統計
    getStateStatistics() {
        const stats = {};
        this.stateHistory.forEach(entry => {
            stats[entry.to] = (stats[entry.to] || 0) + 1;
        });
        return stats;
    }

    // 清理過期數據
    cleanup() {
        console.log('🧹 清理過期的遊戲狀態數據');

        // 清理過期的歷史記錄
        const oneHourAgo = Date.now() - 3600000;
        this.stateHistory = this.stateHistory.filter(entry =>
            entry.timestamp > oneHourAgo
        );

        // 清理過期的同步隊列
        this.syncQueue = this.syncQueue.filter(item =>
            Date.now() - item.timestamp < 300000 // 5分鐘
        );
    }

    // 導出狀態數據
    exportStateData() {
        const data = {
            currentState: this.currentState,
            gameData: this.gameData,
            stateHistory: this.stateHistory,
            statistics: this.getStateStatistics(),
            exportTime: Date.now()
        };

        const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `game-state-${Date.now()}.json`;
        a.click();
        URL.revokeObjectURL(url);
    }
}

// 創建全局實例（避免重複創建）
if (!window.gameStateManager) {
    window.gameStateManager = new GameStateManager();
}

// 添加全局調試命令
window.debugGameState = {
    current: () => window.gameStateManager.getCurrentState(),
    history: () => window.gameStateManager.getStateHistory(),
    stats: () => window.gameStateManager.getStateStatistics(),
    rollback: () => window.gameStateManager.rollbackToPreviousState(),
    reset: () => window.gameStateManager.resetToInitialState(),
    export: () => window.gameStateManager.exportStateData()
};

// 導出供其他模組使用
if (typeof module !== 'undefined' && module.exports) {
    module.exports = GameStateManager;
}
