/**
 * P1P2方塊大亂鬥 - 系統健康監控模組
 * 負責監控系統狀態、錯誤處理、性能監控
 */

class SystemMonitor {
    constructor() {
        this.isEnabled = true;
        this.metrics = {
            errors: [],
            performance: {},
            connections: {},
            memory: {},
            lastUpdate: Date.now()
        };

        this.thresholds = {
            errorRate: 10, // 每分鐘最多10個錯誤
            memoryUsage: 100 * 1024 * 1024, // 100MB
            responseTime: 5000, // 5秒
            reconnectAttempts: 3
        };

        this.init();
    }

    init() {
        console.log('🔍 系統監控模組初始化...');

        // 設置全局錯誤處理
        this.setupGlobalErrorHandling();

        // 設置性能監控
        this.setupPerformanceMonitoring();

        // 設置連接監控
        this.setupConnectionMonitoring();

        // 設置內存監控
        this.setupMemoryMonitoring();

        // 開始定期檢查
        this.startHealthCheck();

        console.log('✅ 系統監控模組已啟動');
    }

    // 全局錯誤處理
    setupGlobalErrorHandling() {
        // JavaScript錯誤
        window.addEventListener('error', (event) => {
            this.recordError('javascript', {
                message: event.message,
                filename: event.filename,
                lineno: event.lineno,
                colno: event.colno,
                stack: event.error?.stack
            });
        });

        // Promise錯誤
        window.addEventListener('unhandledrejection', (event) => {
            this.recordError('promise', {
                reason: event.reason,
                stack: event.reason?.stack
            });
        });

        // 資源載入錯誤
        window.addEventListener('error', (event) => {
            if (event.target !== window) {
                this.recordError('resource', {
                    element: event.target.tagName,
                    source: event.target.src || event.target.href,
                    message: '資源載入失敗'
                });
            }
        }, true);
    }

    // 性能監控
    setupPerformanceMonitoring() {
        // 監控頁面載入性能
        if (window.performance && window.performance.timing) {
            window.addEventListener('load', () => {
                setTimeout(() => {
                    const timing = window.performance.timing;
                    this.metrics.performance.pageLoad = {
                        domContentLoaded: timing.domContentLoadedEventEnd - timing.navigationStart,
                        loadComplete: timing.loadEventEnd - timing.navigationStart,
                        firstPaint: this.getFirstPaint(),
                        timestamp: Date.now()
                    };
                }, 0);
            });
        }

        // 監控FPS
        this.startFPSMonitoring();
    }

    // FPS監控
    startFPSMonitoring() {
        let frames = 0;
        let lastTime = performance.now();

        const countFPS = () => {
            frames++;
            const currentTime = performance.now();

            if (currentTime >= lastTime + 1000) {
                this.metrics.performance.fps = Math.round((frames * 1000) / (currentTime - lastTime));
                frames = 0;
                lastTime = currentTime;

                // 檢查FPS是否過低
                if (this.metrics.performance.fps < 30) {
                    this.recordWarning('performance', `FPS過低: ${this.metrics.performance.fps}`);
                }
            }

            requestAnimationFrame(countFPS);
        };

        requestAnimationFrame(countFPS);
    }

    // 連接監控
    setupConnectionMonitoring() {
        // 監控網絡狀態
        window.addEventListener('online', () => {
            this.metrics.connections.online = true;
            this.recordInfo('network', '網絡連接已恢復');
        });

        window.addEventListener('offline', () => {
            this.metrics.connections.online = false;
            this.recordWarning('network', '網絡連接中斷');
        });

        // 初始化網絡狀態
        this.metrics.connections.online = navigator.onLine;
    }

    // 內存監控
    setupMemoryMonitoring() {
        if (window.performance && window.performance.memory) {
            setInterval(() => {
                const memory = window.performance.memory;
                this.metrics.memory = {
                    used: memory.usedJSHeapSize,
                    total: memory.totalJSHeapSize,
                    limit: memory.jsHeapSizeLimit,
                    timestamp: Date.now()
                };

                // 檢查內存使用是否過高
                if (memory.usedJSHeapSize > this.thresholds.memoryUsage) {
                    this.recordWarning('memory', `內存使用過高: ${Math.round(memory.usedJSHeapSize / 1024 / 1024)}MB`);
                }
            }, 30000); // 每30秒檢查一次
        }
    }

    // 健康檢查
    startHealthCheck() {
        setInterval(() => {
            this.performHealthCheck();
        }, 60000); // 每分鐘檢查一次
    }

    performHealthCheck() {
        const now = Date.now();
        const report = {
            timestamp: now,
            status: 'healthy',
            issues: []
        };

        // 檢查錯誤率
        const recentErrors = this.metrics.errors.filter(error =>
            now - error.timestamp < 60000 // 最近1分鐘
        );

        if (recentErrors.length > this.thresholds.errorRate) {
            report.status = 'warning';
            report.issues.push(`錯誤率過高: ${recentErrors.length}/分鐘`);
        }

        // 檢查FPS
        if (this.metrics.performance.fps && this.metrics.performance.fps < 30) {
            report.status = 'warning';
            report.issues.push(`FPS過低: ${this.metrics.performance.fps}`);
        }

        // 檢查網絡狀態
        if (!this.metrics.connections.online) {
            report.status = 'error';
            report.issues.push('網絡連接中斷');
        }

        // 記錄健康報告
        this.recordHealthReport(report);

        // 如果有問題，嘗試自動修復
        if (report.status !== 'healthy') {
            this.attemptAutoRecovery(report);
        }
    }

    // 記錄錯誤
    recordError(type, details) {
        const error = {
            type,
            details,
            timestamp: Date.now(),
            userAgent: navigator.userAgent,
            url: window.location.href
        };

        this.metrics.errors.push(error);

        // 保持錯誤記錄在合理範圍內
        if (this.metrics.errors.length > 100) {
            this.metrics.errors = this.metrics.errors.slice(-50);
        }

        console.error('🚨 系統錯誤記錄:', error);

        // 發送錯誤報告到服務器（如果需要）
        this.sendErrorReport(error);
    }

    // 記錄警告
    recordWarning(type, message) {
        console.warn(`⚠️ 系統警告 [${type}]:`, message);
    }

    // 記錄信息
    recordInfo(type, message) {
        console.log(`ℹ️ 系統信息 [${type}]:`, message);
    }

    // 獲取首次繪製時間
    getFirstPaint() {
        if (window.performance && window.performance.getEntriesByType) {
            const paintEntries = window.performance.getEntriesByType('paint');
            const firstPaint = paintEntries.find(entry => entry.name === 'first-paint');
            return firstPaint ? firstPaint.startTime : null;
        }
        return null;
    }

    // 記錄健康報告
    recordHealthReport(report) {
        console.log('📊 系統健康報告:', report);

        // 可以發送到服務器進行監控
        if (report.status !== 'healthy') {
            this.sendHealthReport(report);
        }
    }

    // 自動恢復嘗試
    attemptAutoRecovery(report) {
        console.log('🔧 嘗試自動恢復...');

        // 清理內存
        if (report.issues.some(issue => issue.includes('內存'))) {
            this.cleanupMemory();
        }

        // 重連WebSocket
        if (report.issues.some(issue => issue.includes('網絡'))) {
            this.attemptReconnection();
        }

        // 重置性能計數器
        if (report.issues.some(issue => issue.includes('FPS'))) {
            this.optimizePerformance();
        }
    }

    // 清理內存
    cleanupMemory() {
        console.log('🧹 執行內存清理...');

        // 清理舊的錯誤記錄
        this.metrics.errors = this.metrics.errors.slice(-20);

        // 觸發垃圾回收（如果可能）
        if (window.gc) {
            window.gc();
        }

        // 清理DOM中的事件監聽器
        this.cleanupEventListeners();
    }

    // 清理事件監聽器
    cleanupEventListeners() {
        // 移除不必要的事件監聽器
        const elements = document.querySelectorAll('[data-temp-listener]');
        elements.forEach(element => {
            element.removeAttribute('data-temp-listener');
            // 這裡可以添加具體的清理邏輯
        });
    }

    // 嘗試重連
    attemptReconnection() {
        console.log('🔄 嘗試重新連接...');

        // 重連WebSocket
        if (window.gameMatch && typeof window.gameMatch.initializeWebSocket === 'function') {
            window.gameMatch.initializeWebSocket();
        }

        // 重連戰鬥同步
        if (window.battleSync && typeof window.battleSync.connectWebSocket === 'function') {
            window.battleSync.connectWebSocket();
        }
    }

    // 性能優化
    optimizePerformance() {
        console.log('⚡ 執行性能優化...');

        // 降低動畫質量
        document.body.classList.add('low-performance-mode');

        // 減少不必要的計算
        this.reduceComputations();
    }

    // 減少計算
    reduceComputations() {
        // 降低更新頻率
        if (window.battleArena && window.battleArena.setUpdateRate) {
            window.battleArena.setUpdateRate(30); // 降低到30FPS
        }
    }

    // 發送錯誤報告
    sendErrorReport(error) {
        // 這裡可以實現發送錯誤到服務器的邏輯
        if (this.shouldSendReport()) {
            fetch('/api/error-report', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(error)
            }).catch(() => {
                // 忽略發送失敗
            });
        }
    }

    // 發送健康報告
    sendHealthReport(report) {
        if (this.shouldSendReport()) {
            fetch('/api/health-report', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(report)
            }).catch(() => {
                // 忽略發送失敗
            });
        }
    }

    // 判斷是否應該發送報告
    shouldSendReport() {
        // 避免過於頻繁的報告
        const lastReport = localStorage.getItem('lastErrorReport');
        const now = Date.now();

        if (!lastReport || now - parseInt(lastReport) > 60000) { // 1分鐘間隔
            localStorage.setItem('lastErrorReport', now.toString());
            return true;
        }

        return false;
    }

    // 獲取系統狀態
    getSystemStatus() {
        return {
            metrics: this.metrics,
            thresholds: this.thresholds,
            isHealthy: this.isSystemHealthy()
        };
    }

    // 檢查系統是否健康
    isSystemHealthy() {
        const now = Date.now();
        const recentErrors = this.metrics.errors.filter(error =>
            now - error.timestamp < 60000
        );

        return recentErrors.length <= this.thresholds.errorRate &&
               this.metrics.connections.online &&
               (!this.metrics.performance.fps || this.metrics.performance.fps >= 30);
    }

    // 啟用/禁用監控
    setEnabled(enabled) {
        this.isEnabled = enabled;
        console.log(`📊 系統監控已${enabled ? '啟用' : '禁用'}`);
    }

    // 手動觸發健康檢查
    triggerHealthCheck() {
        this.performHealthCheck();
    }

    // 重置監控數據
    resetMetrics() {
        this.metrics = {
            errors: [],
            performance: {},
            connections: { online: navigator.onLine },
            memory: {},
            lastUpdate: Date.now()
        };
        console.log('🔄 監控數據已重置');
    }

    // 導出監控數據
    exportMetrics() {
        const data = {
            ...this.metrics,
            exportTime: Date.now(),
            userAgent: navigator.userAgent,
            url: window.location.href
        };

        const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `system-metrics-${Date.now()}.json`;
        a.click();
        URL.revokeObjectURL(url);
    }
}

// 創建全局實例
window.systemMonitor = new SystemMonitor();

// 添加全局調試命令
window.debugSystem = {
    status: () => window.systemMonitor.getSystemStatus(),
    check: () => window.systemMonitor.triggerHealthCheck(),
    reset: () => window.systemMonitor.resetMetrics(),
    export: () => window.systemMonitor.exportMetrics(),
    enable: () => window.systemMonitor.setEnabled(true),
    disable: () => window.systemMonitor.setEnabled(false)
};

// 導出供其他模組使用
if (typeof module !== 'undefined' && module.exports) {
    module.exports = SystemMonitor;
}
