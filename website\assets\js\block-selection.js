/**
 * P1P2方塊大亂鬥 - 方塊選擇系統 - 重構版本
 * 簡潔、可靠、易維護的實現
 */

if (typeof BlockSelectionSystem === 'undefined') {
class BlockSelectionSystem {
    constructor() {
        this.selectedElement = null;
        this.isSelectionLocked = false;
        this.selectionTimeLimit = 30; // 30秒選擇時間
        this.selectionTimer = null;
        this.matchData = null;
        this.modal = null;

        // 雙方確認狀態追蹤
        this.playerConfirmed = false;    // 自己是否確認
        this.opponentConfirmed = false;  // 對手是否確認
        this.opponentElement = null;     // 對手選擇的元素
        this.timeoutHandled = false;     // 超時處理標記
        this.pendingTimeouts = [];       // 追蹤所有的 setTimeout
        this.opponentSimulationStarted = false; // 對手模擬是否已啟動

        // 防重複創建的鎖定機制
        this.isCreatingModal = false;    // 模態框創建鎖
        this.isShowingWaiting = false;   // 等待模態框顯示鎖
        this.lastCreateTime = 0;         // 上次創建時間
        this.createDebounceTime = 100;   // 防抖時間(毫秒)
        this.isSelectionActive = false;  // 選擇流程是否正在進行

        // 元素定義
        this.elements = {
            fire: {
                name: '火元素',
                icon: '🔥',
                description: '攻擊力強，對冰元素有優勢'
            },
            water: {
                name: '水元素',
                icon: '💧',
                description: '防禦力強，對火元素有優勢'
            },
            earth: {
                name: '土元素',
                icon: '🌍',
                description: '生命力強，對雷元素有優勢'
            },
            air: {
                name: '風元素',
                icon: '💨',
                description: '速度快，對土元素有優勢'
            },
            lightning: {
                name: '雷元素',
                icon: '⚡',
                description: '爆發力強，對水元素有優勢'
            }
        };

        console.log('🎮 方塊選擇系統初始化完成');
    }

    /**
     * 開始方塊選擇
     */
    startBlockSelection(matchData) {
        console.log('🚀 開始方塊選擇', matchData);

        // 防止重複調用
        if (this.isSelectionActive) {
            console.log('⚠️ 方塊選擇已經在進行中，跳過重複調用');
            return;
        }

        this.isSelectionActive = true;

        // 立即隱藏主遊戲界面，防止閃爍
        this.hideMainGameInterface();

        // 重置所有狀態
        this.matchData = matchData;
        this.selectedElement = null;
        this.isSelectionLocked = false;
        this.playerConfirmed = false;
        this.opponentConfirmed = false;
        this.opponentElement = null;
        this.timeoutHandled = false; // 重置超時處理標記
        this.pendingTimeouts = [];   // 清空計時器追蹤

        this.createModal();
        this.startTimer();

        console.log('✅ 方塊選擇界面已顯示');
    }

    /**
     * 隱藏主遊戲界面，防止閃爍
     */
    hideMainGameInterface() {
        console.log('🙈 隱藏主遊戲界面');

        // 隱藏主遊戲容器
        const gameContainer = document.querySelector('.game-container');
        if (gameContainer) {
            gameContainer.style.display = 'none';
        }

        // 隱藏遊戲主界面
        const gameMain = document.querySelector('.game-main');
        if (gameMain) {
            gameMain.style.display = 'none';
        }

        // 隱藏匹配界面
        const matchContainer = document.querySelector('.match-container');
        if (matchContainer) {
            matchContainer.style.display = 'none';
        }

        // 隱藏所有可能的遊戲界面元素
        const gameElements = document.querySelectorAll('.game-interface, .match-interface, .queue-interface');
        gameElements.forEach(element => {
            element.style.display = 'none';
        });

        // 設置body背景，確保視覺一致性
        document.body.style.background = 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)';

        console.log('✅ 主遊戲界面已隱藏');
    }

    /**
     * 創建選擇界面
     */
    createModal() {
        // 防抖檢查
        const now = Date.now();
        if (now - this.lastCreateTime < this.createDebounceTime) {
            console.log('⚠️ 創建請求過於頻繁，跳過');
            return;
        }

        // 防止重複創建
        if (this.isCreatingModal) {
            console.log('⚠️ 模態框正在創建中，跳過重複請求');
            return;
        }

        this.lastCreateTime = now;
        this.isCreatingModal = true;

        try {
            // 立即設置背景遮罩，防止看到底層界面
            document.body.style.overflow = 'hidden';

            // 移除舊的模態框（加強清理）
            const existingModals = document.querySelectorAll('.block-selection-modal');
            existingModals.forEach(modal => {
                modal.remove();
            });

            // 清理等待模態框
            const existingWaitingModals = document.querySelectorAll('.waiting-modal-overlay');
            existingWaitingModals.forEach(modal => {
                modal.remove();
            });

            // 創建新的模態框
            this.modal = document.createElement('div');
            this.modal.className = 'block-selection-modal';

            // 立即設置為可見，確保覆蓋底層界面
            this.modal.style.position = 'fixed';
            this.modal.style.top = '0';
            this.modal.style.left = '0';
            this.modal.style.width = '100%';
            this.modal.style.height = '100%';
            this.modal.style.zIndex = '9999';
            this.modal.style.background = 'rgba(0, 0, 0, 0.8)';

            // 設置初始狀態以防止閣爍
            this.modal.style.opacity = '0';
            this.modal.style.transform = 'scale(0.95)';
            this.modal.innerHTML = this.getModalHTML();

            // 立即添加到頁面，確保覆蓋
            document.body.appendChild(this.modal);

            // 強制重繪以確保 DOM 更新
            this.modal.offsetHeight;

            // 立即顯示，然後平滑動畫
            this.modal.style.opacity = '1';
            setTimeout(() => {
                this.modal.style.transition = 'transform 0.2s ease';
                this.modal.style.transform = 'scale(1)';
            }, 10);

            // 綁定事件
            this.bindEvents();

            console.log('✅ 模態框創建完成');

        } catch (error) {
            console.error('❌ 模態框創建失敗:', error);
        } finally {
            // 釋放鎖定
            this.isCreatingModal = false;
        }
    }

    /**
     * 獲取模態框HTML
     */
    getModalHTML() {
        const opponentName = this.getOpponentName();
        const opponentAvatar = this.getOpponentAvatar();

        return `
            <div class="modal-content">
                <div class="modal-header">
                    <h3>🎯 選擇你的戰鬥元素</h3>
                    <div class="selection-timer">
                        <i class="fas fa-clock"></i>
                        <span id="selection-countdown">${this.selectionTimeLimit}</span>秒
                    </div>
                </div>

                <div class="modal-body">
                    <div class="selection-content">
                        <!-- 對手信息 -->
                        <div class="opponent-info">
                            <div class="opponent-avatar">
                                <img src="${opponentAvatar}" alt="對手">
                            </div>
                            <div class="opponent-details">
                                <div class="opponent-name">對戰對手: ${opponentName}</div>
                                <div class="opponent-status">
                                    <i class="fas fa-hourglass-half"></i>
                                    選擇中...
                                </div>
                            </div>
                        </div>

                        <!-- 元素選擇 -->
                        <div class="elements-selection">
                            <h4>選擇你的戰鬥元素</h4>
                            <div class="elements-grid">
                                ${this.getElementsHTML()}
                            </div>
                        </div>

                        <!-- 操作按鈕 -->
                        <div class="selection-actions">
                            <button class="btn btn-secondary" id="random-selection">
                                <i class="fas fa-dice"></i>
                                隨機選擇
                            </button>
                            <button class="btn btn-primary" id="confirm-selection" disabled>
                                <i class="fas fa-check"></i>
                                確認選擇
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * 獲取元素卡片HTML
     */
    getElementsHTML() {
        return Object.keys(this.elements).map(elementId => {
            const element = this.elements[elementId];
            return `
                <div class="element-card" data-element="${elementId}">
                    <div class="element-icon">${element.icon}</div>
                    <div class="element-name">${element.name}</div>
                    <div class="element-description">${element.description}</div>
                </div>
            `;
        }).join('');
    }

    /**
     * 綁定事件
     */
    bindEvents() {
        // 元素卡片點擊事件
        const elementCards = this.modal.querySelectorAll('.element-card');
        elementCards.forEach(card => {
            card.addEventListener('click', () => {
                const elementId = card.getAttribute('data-element');
                this.selectElement(elementId);
            });
        });

        // 確認按鈕
        const confirmBtn = this.modal.querySelector('#confirm-selection');
        if (confirmBtn) {
            confirmBtn.addEventListener('click', () => {
                this.confirmSelection();
            });
        }

        // 隨機選擇按鈕
        const randomBtn = this.modal.querySelector('#random-selection');
        if (randomBtn) {
            randomBtn.addEventListener('click', () => {
                this.randomSelect();
            });
        }

        console.log('✅ 事件綁定完成');
    }

    /**
     * 選擇元素
     */
    selectElement(elementId) {
        if (this.isSelectionLocked) {
            console.log('❌ 選擇已鎖定');
            return;
        }

        if (!this.elements[elementId]) {
            console.log('❌ 無效的元素ID:', elementId);
            return;
        }

        console.log('🎯 選擇元素:', elementId);

        // 移除所有選中狀態
        const allCards = this.modal.querySelectorAll('.element-card');
        allCards.forEach(card => {
            card.classList.remove('selected');
        });

        // 設置新的選中狀態
        const selectedCard = this.modal.querySelector(`[data-element="${elementId}"]`);
        if (selectedCard) {
            selectedCard.classList.add('selected');
            this.selectedElement = elementId;

            // 啟用確認按鈕
            const confirmBtn = this.modal.querySelector('#confirm-selection');
            if (confirmBtn) {
                confirmBtn.disabled = false;
                confirmBtn.style.opacity = '1';
                confirmBtn.style.pointerEvents = 'auto';
            }

            console.log('✅ 元素選擇成功:', this.elements[elementId].name);
        }
    }

    /**
     * 隨機選擇元素
     */
    randomSelect() {
        const elementIds = Object.keys(this.elements);
        const randomId = elementIds[Math.floor(Math.random() * elementIds.length)];
        this.selectElement(randomId);
        console.log('🎲 隨機選擇:', randomId);
    }

    /**
     * 確認選擇
     */
    confirmSelection() {
        if (!this.selectedElement) {
            alert('請先選擇一個元素！');
            return;
        }

        if (this.playerConfirmed) {
            console.log('❌ 已經確認過了');
            return;
        }

        console.log('✅ 確認選擇:', this.selectedElement);

        this.playerConfirmed = true;

        // 發送選擇到服務器
        this.sendSelectionToServer();

        // 更新界面狀態
        this.updateUIAfterConfirm();

        // 顯示等待遊戲開始的彈窗
        this.showWaitingModal();

        // 檢查是否雙方都已確認
        this.checkBothPlayersReady();
    }

    /**
     * 發送選擇到服務器
     */
    sendSelectionToServer() {
        // 優先嘗試 WebSocket 連接
        let socket = null;
        let socketSource = null;

        // 檢查所有可能的 WebSocket 連接
        console.log('🔍 檢查 WebSocket 連接:');
        console.log('  - gameMatch:', typeof gameMatch, gameMatch);
        console.log('  - gameMatch.socket:', gameMatch?.socket);
        console.log('  - gameMatch.socket.readyState:', gameMatch?.socket?.readyState);

        if (gameMatch && gameMatch.socket && gameMatch.socket.readyState === WebSocket.OPEN) {
            socket = gameMatch.socket;
            socketSource = 'gameMatch';
            console.log('✅ 使用 gameMatch WebSocket 連接');
        } else if (window.gameWebSocket && window.gameWebSocket.readyState === WebSocket.OPEN) {
            socket = window.gameWebSocket;
            socketSource = 'gameWebSocket';
            console.log('✅ 使用 gameWebSocket 連接');
        } else {
            console.log('❌ 所有 WebSocket 連接都不可用:');
            if (gameMatch && gameMatch.socket) {
                console.log('  - gameMatch.socket.readyState:', gameMatch.socket.readyState);
                console.log('  - WebSocket.CONNECTING:', WebSocket.CONNECTING);
                console.log('  - WebSocket.OPEN:', WebSocket.OPEN);
                console.log('  - WebSocket.CLOSING:', WebSocket.CLOSING);
                console.log('  - WebSocket.CLOSED:', WebSocket.CLOSED);
            } else {
                console.log('  - gameMatch.socket: 不存在');
            }
            if (window.gameWebSocket) {
                console.log('  - window.gameWebSocket.readyState:', window.gameWebSocket.readyState);
            } else {
                console.log('  - window.gameWebSocket: 不存在');
            }
        }

        if (socket) {
            const message = {
                type: 'block_selection',
                data: {
                    matchId: this.matchData?.matchId || this.matchData?.roomId || 'unknown',
                    element: this.selectedElement
                }
            };

            console.log('📤 準備發送的消息:', message);
            console.log('📊 當前匹配數據:', this.matchData);
            console.log('🔗 WebSocket 連接狀態:', socket.readyState);

            try {
                socket.send(JSON.stringify(message));
                console.log(`📤 選擇已通過 ${socketSource} 發送到服務器:`, message);

                // 設置一個備用計時器，如果 5 秒內沒有收到對手回應，就使用 HTTP API
                const backupTimeoutId = setTimeout(() => {
                    if (!this.opponentConfirmed) {
                        console.log('⚠️ 5秒內沒有收到對手回應，嘗試 HTTP API');
                        this.sendSelectionViaAPI();
                    }
                }, 5000);
                this.pendingTimeouts.push(backupTimeoutId);

            } catch (error) {
                console.error('❌ WebSocket 發送失敗:', error);
                this.sendSelectionViaAPI();
            }
        } else {
            console.log('❌ WebSocket連接不可用，嘗試重新連接...');

            // 嘗試重新連接 WebSocket
            if (gameMatch && typeof gameMatch.initializeWebSocket === 'function') {
                console.log('🔄 嘗試重新連接 WebSocket');
                gameMatch.initializeWebSocket();

                // 等待 2 秒再嘗試發送
                setTimeout(() => {
                    if (gameMatch.socket && gameMatch.socket.readyState === WebSocket.OPEN) {
                        console.log('✅ WebSocket 重連成功，重新發送');
                        this.sendSelectionToServer();
                    } else {
                        console.log('❌ WebSocket 重連失敗，使用 HTTP API');
                        this.sendSelectionViaAPI();
                    }
                }, 2000);
            } else {
                console.log('❌ 無法重連 WebSocket，使用 HTTP API');
                this.sendSelectionViaAPI();
            }
        }
    }

    /**
     * 通過 HTTP API 發送選擇
     */
    sendSelectionViaAPI() {
        const token = localStorage.getItem('token');

        fetch('/api/game/select-element', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${token}`
            },
            body: JSON.stringify({
                matchId: this.matchData?.matchId || this.matchData?.roomId || 'test-match',
                element: this.selectedElement,
                confirmed: true
            })
        })
        .then(response => response.json())
        .then(data => {
            console.log('📤 HTTP API 選擇發送成功:', data);
            // 等待真實對手選擇，不再模擬
        })
        .catch(error => {
            console.error('❌ HTTP API 選擇發送失敗:', error);
        });
    }

    /**
     * 更新確認後的界面
     */
    updateUIAfterConfirm() {
        // 禁用所有元素卡片
        const allCards = this.modal.querySelectorAll('.element-card');
        allCards.forEach(card => {
            card.style.pointerEvents = 'none';
            card.style.opacity = '0.6';
        });

        // 保持選中的卡片高亮
        const selectedCard = this.modal.querySelector('.element-card.selected');
        if (selectedCard) {
            selectedCard.style.opacity = '1';
        }

        // 更新按鈕狀態
        const confirmBtn = this.modal.querySelector('#confirm-selection');
        const randomBtn = this.modal.querySelector('#random-selection');

        if (confirmBtn) {
            confirmBtn.disabled = true;
            confirmBtn.innerHTML = '<i class="fas fa-check"></i> 已確認';
            confirmBtn.style.background = '#10b981';
        }

        if (randomBtn) {
            randomBtn.disabled = true;
            randomBtn.style.opacity = '0.5';
        }

        console.log('✅ 界面狀態更新完成');
    }

    /**
     * 檢查雙方是否都已準備就緒
     */
    checkBothPlayersReady() {
        console.log('🔍 檢查雙方狀態:', {
            playerConfirmed: this.playerConfirmed,
            opponentConfirmed: this.opponentConfirmed,
            playerElement: this.selectedElement,
            opponentElement: this.opponentElement
        });

        if (this.playerConfirmed && this.opponentConfirmed) {
            console.log('🎉 雙方都已確認，提早開始遊戲！');

            // 停止倒數計時
            this.stopTimer();

            // 顯示提早開始訊息
            this.showEarlyStartMessage();

            // 0.5秒後開始遊戲（大幅減少等待時間）
            const earlyStartTimeoutId = setTimeout(() => {
                this.startGame();
            }, 500);
            this.pendingTimeouts.push(earlyStartTimeoutId);
        }
    }

    /**
     * 顯示提早開始訊息
     */
    showEarlyStartMessage() {
        const countdownElement = this.modal.querySelector('#selection-countdown');
        const timerElement = this.modal.querySelector('.selection-timer');

        if (countdownElement && timerElement) {
            countdownElement.textContent = '提早開始';
            countdownElement.style.color = '#10b981';
            timerElement.style.background = 'rgba(16, 185, 129, 0.2)';
            timerElement.innerHTML = `
                <i class="fas fa-rocket" style="color: #10b981;"></i>
                <span style="color: #10b981; font-weight: 700;">提早開始！</span>
            `;
        }

        // 更新對手狀態顯示
        const opponentStatus = this.modal.querySelector('.opponent-status');
        if (opponentStatus) {
            opponentStatus.innerHTML = `
                <i class="fas fa-check-circle" style="color: #10b981;"></i>
                已確認: ${this.elements[this.opponentElement]?.name || this.opponentElement}
            `;
        }

        console.log('✅ 提早開始訊息已顯示');
    }

    /**
     * 開始倒數計時
     */
    startTimer() {
        let timeLeft = this.selectionTimeLimit;

        // 初始化顯示
        const countdownElement = this.modal.querySelector('#selection-countdown');
        if (countdownElement) {
            countdownElement.textContent = timeLeft;
        }

        this.selectionTimer = setInterval(() => {
            timeLeft--;

            if (countdownElement) {
                countdownElement.textContent = timeLeft;

                // 時間不足時變紅
                if (timeLeft <= 10) {
                    countdownElement.style.color = '#ef4444';
                    countdownElement.parentElement.style.background = 'rgba(239, 68, 68, 0.2)';
                }
            }

            if (timeLeft <= 0) {
                // 立即停止計時器以防止重複調用
                this.stopTimer();
                this.handleTimeout();
            }
        }, 1000);

        console.log('⏰ 倒數計時開始:', this.selectionTimeLimit, '秒');
    }

    /**
     * 停止倒數計時
     */
    stopTimer() {
        if (this.selectionTimer) {
            clearInterval(this.selectionTimer);
            this.selectionTimer = null;
            console.log('⏰ 倒數計時停止');
        }

        // 清理所有的 setTimeout
        if (this.pendingTimeouts && this.pendingTimeouts.length > 0) {
            this.pendingTimeouts.forEach(timeoutId => {
                clearTimeout(timeoutId);
            });
            this.pendingTimeouts = [];
            console.log('⏰ 清理所有待處理的計時器');
        }
    }

    /**
     * 處理超時
     */
    handleTimeout() {
        console.log('⏰ 選擇超時');

        // 防止重複處理
        if (this.timeoutHandled) {
            console.log('⏰ 超時已處理，跳過');
            return;
        }
        this.timeoutHandled = true;

        // 強制停止所有計時器
        this.stopTimer();

        if (!this.playerConfirmed) {
            console.log('⏰ 玩家未確認，自動處理');

            if (!this.selectedElement) {
                // 自動隨機選擇
                console.log('⏰ 自動隨機選擇');
                this.randomSelect();
            }

            // 自動確認選擇（減少等待時間）
            const timeoutId = setTimeout(() => {
                if (!this.playerConfirmed) {
                    console.log('⏰ 自動確認選擇');
                    this.confirmSelection();
                }
            }, 200);
            this.pendingTimeouts.push(timeoutId);
        } else {
            // 已經確認，等待對手選擇
            console.log('⏰ 自己已確認，等待對手選擇');

            // 檢查對手是否已經選擇，如果都已確認則直接開始
            if (this.opponentConfirmed) {
                console.log('⏰ 對手也已確認，直接開始遊戲');
                this.startGame();
            } else {
                console.log('⏰ 等待真實對手選擇...');
                // 顯示等待對手的訊息
                this.showWaitingForOpponent();

                // 給對手更多時間，但最終還是要開始遊戲（減少等待時間）
                const finalTimeoutId = setTimeout(() => {
                    console.log('⏰ 最終超時，強制開始遊戲');
                    this.startGame();
                }, 5000); // 5秒後強制開始（從30秒減少到5秒）
                this.pendingTimeouts.push(finalTimeoutId);
            }
        }
    }

    /**
     * 顯示等待對手的訊息
     */
    showWaitingForOpponent() {
        const countdownElement = this.modal.querySelector('#selection-countdown');
        const timerElement = this.modal.querySelector('.selection-timer');

        if (countdownElement && timerElement) {
            countdownElement.textContent = '等待對手...';
            countdownElement.style.color = '#f59e0b';
            timerElement.style.background = 'rgba(245, 158, 11, 0.2)';
        }

        console.log('⏰ 顯示等待對手訊息');
    }

    /**
     * 處理對手選擇
     */
    handleOpponentSelection(element) {
        console.log('👥 對手已選擇:', element);
        console.log('🔍 當前 modal 狀態:', this.modal);

        this.opponentElement = element;
        this.opponentConfirmed = true;

        // 更新原有選擇彈窗的對手狀態顯示
        const opponentStatus = this.modal?.querySelector('.opponent-status');
        console.log('🔍 找到對手狀態元素:', opponentStatus);

        if (opponentStatus) {
            // 只顯示已選擇狀態，不顯示具體元素
            opponentStatus.innerHTML = `
                <i class="fas fa-check-circle" style="color: #10b981;"></i>
                已選擇
            `;

            console.log('✅ 對手狀態已更新（不顯示具體元素）');
        } else {
            console.error('❌ 找不到對手狀態元素');

            // 嘗試在整個文檔中查找
            const allOpponentStatus = document.querySelectorAll('.opponent-status');
            console.log('🔍 文檔中所有的 .opponent-status 元素:', allOpponentStatus);
        }

        // 更新等待彈窗狀態
        this.updateWaitingModal();

        // 檢查是否雙方都已確認
        this.checkBothPlayersReady();
    }

    /**
     * 開始遊戲
     */
    startGame() {
        console.log('🎮 開始遊戲!');

        // 關閉等待彈窗
        this.closeWaitingModal();

        // 關閉選擇界面
        this.closeSelection();

        // 跳轉到戰鬥界面
        showNotification('遊戲開始！正在載入戰鬥界面...', 'success');

        // 減少延遲，讓用戶更快進入戰鬥
        setTimeout(() => {
            this.enterBattleScreen();
        }, 500);
    }

    /**
     * 進入戰鬥界面
     */
    enterBattleScreen() {
        console.log('🎮 進入戰鬥界面');

        // 準備戰鬥數據
        const battleData = {
            matchId: this.matchData?.matchId || 'battle-' + Date.now(),
            players: {
                self: {
                    id: 'self',
                    name: this.getPlayerName(),
                    element: this.selectedElement,
                    avatar: this.getPlayerAvatar()
                },
                opponent: {
                    id: 'opponent',
                    name: this.getOpponentName(),
                    element: this.opponentElement || 'water',
                    avatar: this.getOpponentAvatar()
                }
            },
            playerElement: this.selectedElement,
            opponentElement: this.opponentElement || 'water'
        };

        // 存儲戰鬥數據
        sessionStorage.setItem('battleData', JSON.stringify(battleData));
        console.log('💾 戰鬥數據已存儲:', battleData);

        // 跳轉到戰鬥頁面
        window.location.href = '/battle.html';
    }

    /**
     * 獲取玩家名稱
     */
    getPlayerName() {
        const user = JSON.parse(localStorage.getItem('user') || '{}');
        return user.username || user.email || '玩家';
    }

    /**
     * 關閉選擇界面
     */
    closeSelection() {
        this.stopTimer();

        if (this.modal) {
            this.modal.remove();
            this.modal = null;
        }

        // 重置選擇流程狀態
        this.isSelectionActive = false;

        // 恢復主遊戲界面
        this.restoreMainGameInterface();

        console.log('✅ 方塊選擇界面已關閉');
    }

    /**
     * 恢復主遊戲界面
     */
    restoreMainGameInterface() {
        console.log('👁️ 恢復主遊戲界面');

        // 恢復body滾動
        document.body.style.overflow = '';

        // 恢復主遊戲容器
        const gameContainer = document.querySelector('.game-container');
        if (gameContainer) {
            gameContainer.style.display = '';
        }

        // 恢復遊戲主界面
        const gameMain = document.querySelector('.game-main');
        if (gameMain) {
            gameMain.style.display = '';
        }

        // 恢復匹配界面
        const matchContainer = document.querySelector('.match-container');
        if (matchContainer) {
            matchContainer.style.display = '';
        }

        // 恢復所有遊戲界面元素
        const gameElements = document.querySelectorAll('.game-interface, .match-interface, .queue-interface');
        gameElements.forEach(element => {
            element.style.display = '';
        });

        // 恢復body背景
        document.body.style.background = '';

        console.log('✅ 主遊戲界面已恢復');
    }

    /**
     * 獲取對手名稱
     */
    getOpponentName() {
        console.log('🔍 獲取對手名稱，matchData:', this.matchData);

        // 檢查不同的數據結構
        if (this.matchData?.players?.opponent?.name) {
            return this.matchData.players.opponent.name;
        }

        // 檢查 player1/player2 結構
        const currentUserId = this.getCurrentUserId();
        if (this.matchData?.players?.player1 && this.matchData?.players?.player2) {
            const opponent = this.matchData.players.player1.id === currentUserId
                ? this.matchData.players.player2
                : this.matchData.players.player1;
            console.log('🎯 找到對手:', opponent);
            return opponent?.name || '對手';
        }

        console.log('⚠️ 無法找到對手名稱，使用默認值');
        return '對手';
    }

    /**
     * 獲取對手頭像
     */
    getOpponentAvatar() {
        console.log('🔍 獲取對手頭像，matchData:', this.matchData);

        // 檢查不同的數據結構
        if (this.matchData?.players?.opponent) {
            const opponent = this.matchData.players.opponent;
            if (opponent?.discord_avatar) return opponent.discord_avatar;
            if (opponent?.twitch_avatar) return opponent.twitch_avatar;
            if (opponent?.avatar) return opponent.avatar;
        }

        // 檢查 player1/player2 結構
        const currentUserId = this.getCurrentUserId();
        if (this.matchData?.players?.player1 && this.matchData?.players?.player2) {
            const opponent = this.matchData.players.player1.id === currentUserId
                ? this.matchData.players.player2
                : this.matchData.players.player1;
            console.log('🎯 找到對手頭像數據:', opponent);

            if (opponent?.discord_avatar) {
                console.log('✅ 使用對手Discord頭像:', opponent.discord_avatar);
                return opponent.discord_avatar;
            }
            if (opponent?.twitch_avatar) {
                console.log('✅ 使用對手Twitch頭像:', opponent.twitch_avatar);
                return opponent.twitch_avatar;
            }
            if (opponent?.avatar) {
                console.log('✅ 使用對手一般頭像:', opponent.avatar);
                return opponent.avatar;
            }
        }

        console.log('⚠️ 無法找到對手頭像，使用默認頭像');
        return '/assets/images/default-avatar.png';
    }

    /**
     * 獲取玩家頭像
     */
    getPlayerAvatar() {
        // 檢查不同的數據結構
        if (this.matchData?.players?.self) {
            const player = this.matchData.players.self;
            if (player?.discord_avatar) return player.discord_avatar;
            if (player?.twitch_avatar) return player.twitch_avatar;
            if (player?.avatar) return player.avatar;
        }

        // 檢查 player1/player2 結構
        const currentUserId = this.getCurrentUserId();
        if (this.matchData?.players?.player1 && this.matchData?.players?.player2) {
            const player = this.matchData.players.player1.id === currentUserId
                ? this.matchData.players.player1
                : this.matchData.players.player2;

            if (player?.discord_avatar) return player.discord_avatar;
            if (player?.twitch_avatar) return player.twitch_avatar;
            if (player?.avatar) return player.avatar;
        }

        return '/assets/images/default-avatar.png';
    }

    /**
     * 獲取當前用戶ID
     */
    getCurrentUserId() {
        const user = JSON.parse(localStorage.getItem('user') || '{}');
        return user.id || user.user_id || user.email;
    }

    /**
     * 顯示等待遊戲開始的彈窗
     */
    showWaitingModal() {
        // 防止重複顯示
        if (this.isShowingWaiting) {
            console.log('⚠️ 等待彈窗正在顯示中，跳過重複請求');
            return;
        }

        this.isShowingWaiting = true;
        console.log('🕰️ 顯示等待遊戲開始彈窗');

        try {
            // 先關閉原有的選擇彈窗
            if (this.modal) {
                this.modal.style.display = 'none';
            }

            // 清理舊的等待彈窗
            const existingWaitingModals = document.querySelectorAll('.waiting-modal-overlay');
            existingWaitingModals.forEach(modal => modal.remove());

            // 創建等待彈窗
            this.waitingModal = document.createElement('div');
            this.waitingModal.className = 'waiting-modal-overlay';

            // 設置初始狀態以防止閣爍
            this.waitingModal.style.opacity = '0';
            this.waitingModal.innerHTML = `
            <div class="waiting-modal">
                <div class="waiting-header">
                    <div class="waiting-icon">
                        <i class="fas fa-hourglass-half fa-spin"></i>
                    </div>
                    <h2>等待遊戲開始</h2>
                </div>

                <div class="waiting-content">
                    <div class="selection-summary">
                        <div class="player-selection">
                            <div class="selection-avatar">
                                <img src="${this.getPlayerAvatar()}" alt="你的頭像">
                                <div class="element-badge">
                                    ${this.elements[this.selectedElement].icon}
                                </div>
                            </div>
                            <div class="selection-info">
                                <div class="player-name">你</div>
                                <div class="element-name">${this.elements[this.selectedElement].name}</div>
                                <div class="status-indicator confirmed">
                                    <i class="fas fa-check-circle"></i> 已確認
                                </div>
                            </div>
                        </div>

                        <div class="vs-divider">
                            <span>VS</span>
                        </div>

                        <div class="opponent-selection">
                            <div class="selection-avatar">
                                <img src="${this.getOpponentAvatar()}" alt="對手頭像">
                                <div class="element-badge ${this.opponentConfirmed ? 'confirmed' : 'waiting'}">
                                    ${this.opponentConfirmed ? '✓' : '?'}
                                </div>
                            </div>
                            <div class="selection-info">
                                <div class="player-name">${this.getOpponentName()}</div>
                                <div class="element-name">${this.opponentConfirmed ? '已選擇' : '選擇中...'}</div>
                                <div class="status-indicator ${this.opponentConfirmed ? 'confirmed' : 'waiting'}">
                                    <i class="fas ${this.opponentConfirmed ? 'fa-check-circle' : 'fa-clock'}"></i>
                                    ${this.opponentConfirmed ? '已確認' : '等待中'}
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="waiting-message">
                        <div class="message-text" id="waiting-message">
                            ${this.opponentConfirmed ? '雙方已確認，準備開始戰鬥！' : '等待對手選擇元素...'}
                        </div>
                        <div class="loading-dots">
                            <span></span><span></span><span></span>
                        </div>
                    </div>
                </div>
            </div>
            `;

            // 添加到頁面
            document.body.appendChild(this.waitingModal);

            // 強制重繪以確保 DOM 更新
            this.waitingModal.offsetHeight;

            // 平滑顯示動畫
            this.waitingModal.style.transition = 'opacity 0.3s ease';
            this.waitingModal.style.opacity = '1';
            this.waitingModal.classList.add('show');

            console.log('✅ 等待彈窗已顯示');

        } catch (error) {
            console.error('❌ 等待彈窗顯示失敗:', error);
        } finally {
            // 釋放鎖定
            this.isShowingWaiting = false;
        }
    }

    /**
     * 更新等待彈窗狀態
     */
    updateWaitingModal() {
        if (!this.waitingModal) return;

        console.log('🔄 更新等待彈窗狀態');

        // 更新對手狀態
        const opponentBadge = this.waitingModal.querySelector('.opponent-selection .element-badge');
        const opponentElementName = this.waitingModal.querySelector('.opponent-selection .element-name');
        const opponentStatus = this.waitingModal.querySelector('.opponent-selection .status-indicator');
        const waitingMessage = this.waitingModal.querySelector('#waiting-message');

        if (this.opponentConfirmed) {
            if (opponentBadge) {
                opponentBadge.innerHTML = '✓';
                opponentBadge.className = 'element-badge confirmed';
            }
            if (opponentElementName) {
                opponentElementName.textContent = '已選擇';
            }
            if (opponentStatus) {
                opponentStatus.innerHTML = '<i class="fas fa-check-circle"></i> 已確認';
                opponentStatus.className = 'status-indicator confirmed';
            }
            if (waitingMessage) {
                waitingMessage.textContent = '雙方已確認，準備開始戰鬥！';
            }
        }
    }

    /**
     * 關閉等待彈窗
     */
    closeWaitingModal() {
        if (this.waitingModal) {
            console.log('🚪 關閉等待彈窗');
            this.waitingModal.classList.add('hide');
            setTimeout(() => {
                if (this.waitingModal && this.waitingModal.parentNode) {
                    this.waitingModal.parentNode.removeChild(this.waitingModal);
                }
                this.waitingModal = null;
            }, 300);
        }
    }
}
} // 結束 BlockSelectionSystem 類定義檢查

// 創建全局實例（避免重複創建）
if (typeof blockSelection === 'undefined' && typeof BlockSelectionSystem !== 'undefined') {
    var blockSelection = new BlockSelectionSystem();
}

// 全局測試函數
window.testBlockSelection = function() {
    console.log('🧪 測試方塊選擇系統');

    const testMatchData = {
        roomId: 'test-room',
        players: {
            self: {
                id: 'player1',
                name: '測試玩家1'
            },
            opponent: {
                id: 'player2',
                name: '測試玩家2',
                discord_avatar: '/assets/images/default-avatar.png'
            }
        }
    };

    blockSelection.startBlockSelection(testMatchData);
};

window.testSelectElement = function(elementId = 'fire') {
    console.log('🧪 測試選擇元素:', elementId);
    blockSelection.selectElement(elementId);
};

window.testConfirmSelection = function() {
    console.log('🧪 測試確認選擇');
    blockSelection.confirmSelection();
};

window.testOpponentSelection = function(elementId = 'water') {
    console.log('🧪 測試對手選擇:', elementId);
    blockSelection.handleOpponentSelection(elementId);
};

window.testBothConfirm = function() {
    console.log('🧪 測試雙方確認');

    // 自己選擇火元素
    blockSelection.selectElement('fire');
    setTimeout(() => {
        blockSelection.confirmSelection();
    }, 500);

    // 對手選擇水元素
    setTimeout(() => {
        blockSelection.handleOpponentSelection('water');
    }, 1000);
};

// 新增：測試完整流程的函數
window.testCompleteFlow = function() {
    console.log('🚀 測試完整流程');

    // 模擬匹配數據
    const mockMatchData = {
        matchId: 'test-match-' + Date.now(),
        roomId: 'test-room-' + Date.now(),
        players: {
            player1: { id: 'test-player-1', name: '測試玩家1' },
            player2: { id: 'test-player-2', name: '測試玩家2' }
        }
    };

    // 開始方塊選擇
    blockSelection.startBlockSelection(mockMatchData);

    console.log('📝 測試步驟:');
    console.log('1. 選擇一個元素');
    console.log('2. 點擊確認按鈕');
    console.log('3. 等待對手選擇或超時');
    console.log('4. 雙方確認後提早開始');
};

// 新增：測試超時機制
window.testTimeout = function() {
    console.log('⏰ 測試超時機制');

    const mockMatchData = {
        matchId: 'timeout-test-' + Date.now(),
        roomId: 'timeout-room-' + Date.now()
    };

    // 設置短的超時時間來測試
    blockSelection.selectionTimeLimit = 5; // 5秒超時
    blockSelection.startBlockSelection(mockMatchData);

    console.log('📝 5秒後將自動超時處理');
};

// 新增：測試計時器狀態
window.checkTimerStatus = function() {
    console.log('🔍 檢查計時器狀態:');
    console.log('  - selectionTimer:', blockSelection.selectionTimer);
    console.log('  - timeoutHandled:', blockSelection.timeoutHandled);
    console.log('  - playerConfirmed:', blockSelection.playerConfirmed);
    console.log('  - isSelectionLocked:', blockSelection.isSelectionLocked);
};

// 新增：強制重置系統
window.forceReset = function() {
    console.log('🔄 強制重置系統');

    // 停止所有計時器
    if (blockSelection.selectionTimer) {
        clearInterval(blockSelection.selectionTimer);
        blockSelection.selectionTimer = null;
    }

    // 重置所有狀態
    blockSelection.timeoutHandled = false;
    blockSelection.playerConfirmed = false;
    blockSelection.opponentConfirmed = false;
    blockSelection.isSelectionLocked = false;
    blockSelection.selectedElement = null;

    // 關閉界面
    if (blockSelection.modal) {
        blockSelection.modal.remove();
        blockSelection.modal = null;
    }

    console.log('✅ 系統已重置');
};

console.log('🎮 方塊選擇系統重構版本載入完成');
console.log('💡 測試命令:');
console.log('  - testCompleteFlow(): 測試完整流程（推薦）');
console.log('  - testBothConfirm(): 測試雙方確認提早開始');
console.log('  - testTimeout(): 測試超時機制（5秒）');
console.log('🔧 調試命令:');
console.log('  - checkTimerStatus(): 檢查計時器狀態');
console.log('  - forceReset(): 強制重置系統');
console.log('🔍 其他測試:');
console.log('  - testBlockSelection(): 測試基本系統');
console.log('  - testSelectElement("fire"): 測試選擇元素');
console.log('  - testOpponentSelection("water"): 測試對手選擇');
