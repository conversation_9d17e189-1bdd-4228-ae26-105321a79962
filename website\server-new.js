#!/usr/bin/env node

/**
 * P1P2官網服務器 - 支持認證系統
 * 用於本地開發和生產環境
 */

const http = require('http');
const fs = require('fs');
const path = require('path');
const url = require('url');
const querystring = require('querystring');
const { setupAuthRoutes } = require('./auth-api-mongodb');
const { setupUserEconomyRoutes } = require('./user-economy-api');
const { setupDailyRewardsRoutes } = require('./daily-rewards-api');
const database = require('./database');



// WebSocket支持
let GameWebSocketServer;
try {
  GameWebSocketServer = require('./websocket-server');
} catch (error) {
  console.log('⚠️ WebSocket服務器模組未找到，將跳過WebSocket功能');
}

const PORT = process.env.PORT || 8080;
const HOST = process.env.HOST || '0.0.0.0';

// MIME類型映射
const mimeTypes = {
  '.html': 'text/html',
  '.css': 'text/css',
  '.js': 'application/javascript',
  '.json': 'application/json',
  '.png': 'image/png',
  '.jpg': 'image/jpeg',
  '.jpeg': 'image/jpeg',
  '.gif': 'image/gif',
  '.svg': 'image/svg+xml',
  '.ico': 'image/x-icon',
  '.mp4': 'video/mp4',
  '.webm': 'video/webm',
  '.woff': 'font/woff',
  '.woff2': 'font/woff2',
  '.ttf': 'font/ttf',
  '.eot': 'application/vnd.ms-fontobject'
};

// 創建簡單的Express風格路由器
class SimpleRouter {
  constructor() {
    this.routes = {
      GET: new Map(),
      POST: new Map(),
      PUT: new Map(),
      DELETE: new Map()
    };
  }

  get(path, handler) {
    this.routes.GET.set(path, handler);
    console.log(`註冊GET路由: ${path}`);
  }

  post(path, handler) {
    this.routes.POST.set(path, handler);
    console.log(`註冊POST路由: ${path}`);
  }

  put(path, handler) {
    this.routes.PUT.set(path, handler);
  }

  delete(path, handler) {
    this.routes.DELETE.set(path, handler);
  }

  handle(req, res) {
    const method = req.method;
    const pathname = url.parse(req.url).pathname;

    if (this.routes[method] && this.routes[method].has(pathname)) {
      console.log(`✅ 找到路由處理器: ${method} ${pathname}`);
      const handler = this.routes[method].get(pathname);
      try {
        handler(req, res);
        return true;
      } catch (error) {
        console.error(`❌ 路由處理器錯誤:`, error);
        res.status(500).json({
          success: false,
          message: '服務器內部錯誤'
        });
        return true;
      }
    }

    return false;
  }
}

// 創建路由器實例
const router = new SimpleRouter();

// 設置認證API路由
console.log('🔧 正在設置認證API路由...');
setupAuthRoutes(router);
console.log('✅ 認證API路由設置完成');

// 設置用戶經濟系統API路由
console.log('🔧 正在設置用戶經濟系統API路由...');
setupUserEconomyRoutes(router);
console.log('✅ 用戶經濟系統API路由設置完成');

// 設置每日獎勵和成就系統API路由
console.log('🔧 正在設置每日獎勵和成就系統API路由...');
setupDailyRewardsRoutes(router);
console.log('✅ 每日獎勵和成就系統API路由設置完成');

// 設置遊戲API路由
console.log('🔧 正在設置遊戲API路由...');

// 方塊選擇API
router.post('/api/game/select-element', async (req, res) => {
  try {
    console.log('🎮 收到方塊選擇請求:', req.body);

    const { matchId, element } = req.body;

    if (!matchId || !element) {
      return res.status(400).json({
        success: false,
        message: '缺少必要參數: matchId 和 element'
      });
    }

    const validElements = ['fire', 'water', 'earth', 'air', 'lightning'];
    if (!validElements.includes(element)) {
      return res.status(400).json({
        success: false,
        message: '無效的元素類型'
      });
    }

    console.log(`✅ 玩家選擇了 ${element} 元素，比賽 ID: ${matchId}`);

    res.json({
      success: true,
      message: '元素選擇成功',
      data: {
        matchId,
        element,
        timestamp: new Date().toISOString()
      }
    });

  } catch (error) {
    console.error('❌ 方塊選擇API錯誤:', error);
    res.status(500).json({
      success: false,
      message: '服務器內部錯誤'
    });
  }
});

console.log('✅ 遊戲API路由設置完成');

// 解析請求體
function parseBody(req) {
  return new Promise((resolve, reject) => {
    let body = '';
    req.on('data', chunk => {
      body += chunk.toString();
    });
    req.on('end', () => {
      try {
        if (req.headers['content-type'] === 'application/json') {
          resolve(JSON.parse(body));
        } else {
          resolve(querystring.parse(body));
        }
      } catch (error) {
        resolve({});
      }
    });
    req.on('error', reject);
  });
}

// 增強請求和響應對象
function enhanceReqRes(req, res) {
  // 增強請求對象
  req.body = {};

  // 增強響應對象
  res.json = function(data) {
    this.setHeader('Content-Type', 'application/json');
    this.end(JSON.stringify(data));
  };

  res.status = function(code) {
    this.statusCode = code;
    return this;
  };

  res.send = function(data) {
    if (typeof data === 'object') {
      this.json(data);
    } else {
      this.end(data);
    }
  };
}

// 代理到API服務器
function proxyToApiServer(req, res, pathname) {
  const http = require('http');
  const querystring = require('querystring');

  const options = {
    hostname: '127.0.0.1',
    port: 3002,
    path: pathname + (req.url.includes('?') ? '?' + req.url.split('?')[1] : ''),
    method: req.method,
    headers: {
      ...req.headers,
      'host': '127.0.0.1:3002'
    }
  };

  const proxyReq = http.request(options, (proxyRes) => {
    // 複製響應頭
    Object.keys(proxyRes.headers).forEach(key => {
      res.setHeader(key, proxyRes.headers[key]);
    });

    res.statusCode = proxyRes.statusCode;

    // 轉發響應數據
    proxyRes.pipe(res);
  });

  proxyReq.on('error', (err) => {
    console.error('❌ 代理請求錯誤:', err);
    res.writeHead(502, { 'Content-Type': 'application/json' });
    res.end(JSON.stringify({
      success: false,
      message: '管理後台API服務器連接失敗'
    }));
  });

  // 轉發請求數據
  req.pipe(proxyReq);
}

// 獲取文件MIME類型
function getMimeType(filePath) {
  const ext = path.extname(filePath).toLowerCase();
  return mimeTypes[ext] || 'application/octet-stream';
}

// 服務靜態文件
function serveStaticFile(filePath, res) {
  fs.readFile(filePath, (err, data) => {
    if (err) {
      if (err.code === 'ENOENT') {
        res.writeHead(404, { 'Content-Type': 'text/html' });
        res.end(`
          <html>
            <head><title>404 - 頁面未找到</title></head>
            <body style="font-family: Arial, sans-serif; text-align: center; padding: 50px;">
              <h1>404 - 頁面未找到</h1>
              <p>抱歉，您請求的頁面不存在。</p>
              <a href="/" style="color: #ff6b35;">返回首頁</a>
            </body>
          </html>
        `);
      } else {
        res.writeHead(500, { 'Content-Type': 'text/plain' });
        res.end('服務器內部錯誤');
      }
      return;
    }

    const mimeType = getMimeType(filePath);
    const headers = {
      'Content-Type': mimeType,
      'Cache-Control': 'public, max-age=3600'
    };

    // 為字體文件添加CORS頭
    if (mimeType.startsWith('font/') || mimeType.includes('font')) {
      headers['Access-Control-Allow-Origin'] = '*';
    }

    res.writeHead(200, headers);
    res.end(data);
  });
}

// 創建HTTP服務器
const server = http.createServer(async (req, res) => {
  // 增強請求和響應對象
  enhanceReqRes(req, res);

  // 解析請求體（對於POST請求）
  if (req.method === 'POST' || req.method === 'PUT') {
    req.body = await parseBody(req);
  }

  // 設置CORS頭
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');

  // 處理OPTIONS請求
  if (req.method === 'OPTIONS') {
    res.writeHead(200);
    res.end();
    return;
  }

  const parsedUrl = url.parse(req.url, true);
  let pathname = parsedUrl.pathname;

  console.log(`${new Date().toISOString()} - ${req.method} ${pathname}`);

  // 嘗試處理API路由
  const routeHandled = router.handle(req, res);
  if (routeHandled) {
    return;
  }

  // 如果是管理後台API路由，代理到API服務器
  if (pathname.startsWith('/api/admin/')) {
    console.log(`🔄 代理管理後台API請求: ${pathname}`);
    proxyToApiServer(req, res, pathname);
    return;
  }

  // 如果是API路由但沒有找到處理器，返回404 JSON
  if (pathname.startsWith('/api/')) {
    console.log(`❌ API路由未找到: ${pathname}`);
    res.writeHead(404, { 'Content-Type': 'application/json' });
    res.end(JSON.stringify({
      success: false,
      message: `API端點未找到: ${pathname}`
    }));
    return;
  }

  // 健康檢查端點
  if (pathname === '/health') {
    const healthStatus = {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      memory: process.memoryUsage(),
      load: {
        connections: 0, // 這裡可以添加實際的連接數
        cpu: process.cpuUsage(),
        version: '1.0.0'
      },
      services: {
        database: 'connected',
        websocket: 'active'
      }
    };

    res.writeHead(200, { 'Content-Type': 'application/json' });
    res.end(JSON.stringify(healthStatus));
    return;
  }

  // 重啟端點（通過健康檢查觸發）
  if (pathname === '/health-restart') {
    console.log('🔄 通過健康檢查觸發重啟');
    res.writeHead(200, { 'Content-Type': 'application/json' });
    res.end(JSON.stringify({ success: true, message: '服務器正在重啟...', timestamp: new Date().toISOString() }));

    setTimeout(() => {
      console.log('🔄 正在重啟服務器...');
      process.exit(0);
    }, 1000);
    return;
  }

  // 處理靜態文件
  let filePath;

  if (pathname === '/') {
    filePath = path.join(__dirname, 'index.html');
  } else if (pathname === '/auth' || pathname === '/login' || pathname === '/register') {
    filePath = path.join(__dirname, 'auth.html');
  } else if (pathname === '/profile') {
    // 用戶資料頁面
    console.log('👤 訪問用戶資料頁面');
    filePath = path.join(__dirname, 'profile.html');
  } else if (pathname === '/admin') {
    // 管理後台頁面
    console.log('🛠️ 訪問管理後台頁面');
    filePath = path.join(__dirname, 'admin', 'index.html');
  } else if (pathname === '/api/restart') {
    // 重啟 API
    console.log('🔄 重啟服務器請求');
    res.writeHead(200, { 'Content-Type': 'application/json' });
    res.end(JSON.stringify({ success: true, message: '服務器正在重啟...', timestamp: new Date().toISOString() }));

    // 延遲重啟以允許回應發送
    setTimeout(() => {
      console.log('🔄 正在重啟服務器...');
      process.exit(0); // PM2 會自動重啟
    }, 1000);
    return;
  } else if (pathname === '/game/' || pathname === '/game') {
    // 遊戲頁面 - 返回新的遊戲主頁面 (Fixed: 2024-01-23 14:50)
    console.log('🎮 訪問遊戲頁面 - 使用新的 game.html');
    filePath = path.join(__dirname, 'game.html');
  } else if (pathname === '/battle') {
    // 戰鬥頁面
    console.log('⚔️ 訪問戰鬥頁面');
    filePath = path.join(__dirname, 'battle.html');
  } else {
    // 其他靜態文件 - 清理異常路徑
    let cleanPathname = pathname;

    // 檢測並阻止問題爬蟲和異常路徑
    const userAgent = req.headers['user-agent'] || '';
    const clientIP = req.headers['x-forwarded-for'] || req.connection.remoteAddress || '';

    // 阻止導致路徑嵌套問題的爬蟲
    const problematicBots = [
      'ClaudeBot',
      'anthropic.com'
    ];

    const isProblematicBot = problematicBots.some(bot =>
      userAgent.toLowerCase().includes(bot.toLowerCase())
    );

    if (isProblematicBot) {
      console.log(`🚫 阻止問題爬蟲: ${userAgent} from ${clientIP}`);
      res.writeHead(403, {
        'Content-Type': 'text/plain',
        'Cache-Control': 'no-cache, no-store, must-revalidate'
      });
      res.end('Bot access restricted');
      return;
    }

    // 檢測並修復路徑嵌套問題 - 增強版本
    const hasPathNesting = (
      pathname.includes('/js/js/') ||
      pathname.includes('/css/css/') ||
      pathname.includes('/images/images/') ||
      pathname.includes('/elements/elements/') ||
      pathname.includes('/avatars/avatars/') ||
      pathname.includes('/screenshots/screenshots/') ||
      pathname.includes('/dev-logs/js/') ||
      pathname.includes('/currency/images/') ||
      // 檢測重複的路徑段
      /\/([^\/]+)\/\1\//.test(pathname) ||
      // 檢測過長的嵌套路徑（超過10層）
      (pathname.split('/').length > 15)
    );

    if (hasPathNesting) {
      console.log(`⚠️ 檢測到異常路徑嵌套: ${pathname} from ${clientIP}`);
      // 直接返回404，不處理這些異常請求
      res.writeHead(404, {
        'Content-Type': 'text/plain',
        'Cache-Control': 'no-cache, no-store, must-revalidate'
      });
      res.end('Invalid path detected');
      return;
    }

    filePath = path.join(__dirname, cleanPathname);
  }

  // 檢查 filePath 是否已設置
  if (!filePath) {
    console.log(`❌ filePath 未設置，路徑: ${pathname}`);
    res.writeHead(404, { 'Content-Type': 'text/html' });
    res.end(`
      <html>
        <head><title>404 - 頁面未找到</title></head>
        <body style="font-family: Arial, sans-serif; text-align: center; padding: 50px;">
          <h1>404 - 頁面未找到</h1>
          <p>抱歉，您請求的頁面不存在。</p>
          <p>請求路徑: ${pathname}</p>
          <a href="/" style="color: #ff6b35;">返回首頁</a>
        </body>
      </html>
    `);
    return;
  }

  // 檢查文件是否存在
  fs.access(filePath, fs.constants.F_OK, (err) => {
    if (err) {
      // 文件不存在，返回404
      res.writeHead(404, { 'Content-Type': 'text/html' });
      res.end(`
        <html>
          <head><title>404 - 頁面未找到</title></head>
          <body style="font-family: Arial, sans-serif; text-align: center; padding: 50px;">
            <h1>404 - 頁面未找到</h1>
            <p>抱歉，您請求的頁面不存在。</p>
            <p>請求路徑: ${pathname}</p>
            <a href="/" style="color: #ff6b35;">返回首頁</a>
          </body>
        </html>
      `);
      return;
    }

    // 文件存在，提供服務
    serveStaticFile(filePath, res);
  });
});

// 初始化資料庫連接並啟動服務器
async function startServer() {
  try {
    // 連接資料庫
    console.log('🔌 正在初始化資料庫連接...');
    await database.connect();

    // 啟動服務器
    server.listen(PORT, HOST, () => {
      console.log(`
🚀 P1P2官網服務器已啟動！

📍 服務器信息：
   - 地址: http://${HOST}:${PORT}
   - 環境: ${process.env.NODE_ENV || 'development'}
   - 進程ID: ${process.pid}
   - 資料庫: MongoDB Atlas ✅

🌐 訪問地址：
   - 首頁: http://localhost:${PORT}
   - 認證: http://localhost:${PORT}/auth
   - 遊戲: http://localhost:${PORT}/game
   - 健康檢查: http://localhost:${PORT}/health
   - 資料庫狀態: http://localhost:${PORT}/api/auth/db-status

💡 提示：
   - 訪問 /auth 進行登入或註冊
   - 支持 Discord 和 Twitch 社交登入
   - 包含郵件驗證和密碼重設功能
   - 用戶數據存儲在 MongoDB Atlas
   - 按 Ctrl+C 停止服務器
      `);

      // 初始化WebSocket服務器
      if (GameWebSocketServer) {
        try {
          const wsServer = new GameWebSocketServer(server);
          console.log('✅ WebSocket服務器已啟動 - 支持即時匹配功能');
        } catch (error) {
          console.error('❌ WebSocket服務器啟動失敗:', error.message);
        }
      }
    });
  } catch (error) {
    console.error('❌ 服務器啟動失敗:', error.message);
    console.error('請檢查 MongoDB 連接配置和網絡連接');
    process.exit(1);
  }
}

// 啟動服務器
startServer();

// 優雅關閉
async function gracefulShutdown(signal) {
  console.log(`\n🛑 收到${signal}信號，正在關閉服務器...`);

  try {
    // 關閉HTTP服務器
    await new Promise((resolve) => {
      server.close(resolve);
    });
    console.log('✅ HTTP服務器已關閉');

    // 關閉資料庫連接
    await database.close();
    console.log('✅ 資料庫連接已關閉');

    console.log('✅ 服務器已安全關閉');
    process.exit(0);
  } catch (error) {
    console.error('❌ 關閉過程中出現錯誤:', error.message);
    process.exit(1);
  }
}

process.on('SIGINT', () => gracefulShutdown('SIGINT'));
process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
