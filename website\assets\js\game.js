/**
 * P1P2方塊大亂鬥 - 遊戲頁面 JavaScript
 */

// 全局變量（避免重複聲明）
if (typeof currentUser === 'undefined') {
    var currentUser = null;
}
if (typeof currentSection === 'undefined') {
    var currentSection = 'lobby';
}

// DOM 元素（在 DOMContentLoaded 事件中初始化）
let loadingScreen, gameInterface, modalOverlay, notificationContainer;

// 初始化遊戲
function initializeGame() {
    console.log('🎮 初始化遊戲頁面...');

    // 檢查登入狀態
    checkAuthStatus();

    // 初始化事件監聽器
    initializeEventListeners();

    // 初始化導航
    initializeNavigation();

    console.log('✅ 遊戲頁面初始化完成');
}

// 檢查用戶登入狀態
function checkAuthStatus() {
    const user = localStorage.getItem('user');
    const token = localStorage.getItem('token');

    console.log('🔍 檢查遊戲頁面登入狀態:', { user: !!user, token: !!token });

    if (!user || !token) {
        // 未登入，但仍然顯示遊戲界面（功能受限）
        console.log('⚠️ 用戶未登入，顯示訪客模式');
        currentUser = {
            username: '訪客',
            email: '<EMAIL>',
            isGuest: true
        };

        // 顯示遊戲界面
        showGameInterface();

        // 顯示登入提示
        setTimeout(() => {
            showNotification('您正在使用訪客模式，請登入以解鎖完整功能。', 'info');
        }, 2000);
        return;
    }

    try {
        currentUser = JSON.parse(user);
        currentUser.isGuest = false;
        console.log('✅ 用戶已登入:', currentUser.username || currentUser.email);

        // 更新用戶界面
        updateUserInterface();

        // 嘗試刷新用戶數據（獲取最新頭像）
        refreshUserData();

        // 顯示遊戲界面
        showGameInterface();

    } catch (error) {
        console.error('❌ 用戶數據解析錯誤:', error);
        showNotification('登入數據異常，請重新登入。', 'error');
        localStorage.removeItem('user');
        localStorage.removeItem('token');
        setTimeout(() => {
            window.location.href = '/auth';
        }, 2000);
    }
}

// 更新用戶界面
function updateUserInterface() {
    if (!currentUser) return;

    // 調試：顯示當前用戶數據
    console.log('👤 當前用戶數據:', JSON.stringify(currentUser, null, 2));

    // 更新用戶名
    const userNameElement = document.getElementById('user-name');
    if (userNameElement) {
        userNameElement.textContent = currentUser.username || currentUser.email || '玩家';
    }

    // 更新頭像 - 根據登入方式選擇頭像
    const userAvatarElement = document.getElementById('user-avatar');
    if (userAvatarElement) {
        let avatarUrl = getAvatarUrl(currentUser);

        console.log('🖼️ 設置用戶頭像:', avatarUrl);

        // 清除之前的錯誤處理器
        userAvatarElement.onerror = null;

        // 設置新的頭像
        userAvatarElement.src = avatarUrl;

        // 頭像載入成功時的處理
        userAvatarElement.onload = function() {
            console.log('✅ 頭像載入成功:', avatarUrl);
        };

        // 頭像載入失敗時的備用方案
        userAvatarElement.onerror = function() {
            console.log('❌ 頭像載入失敗:', avatarUrl);
            console.log('🔄 嘗試使用默認頭像');

            // 避免無限循環
            this.onerror = null;
            this.src = getDefaultAvatar();
        };
    }

    // 更新等級和資源（模擬數據）
    const userLevelElement = document.getElementById('user-level');
    const userCoinsElement = document.getElementById('user-coins');
    const userGemsElement = document.getElementById('user-gems');
    const userEnergyElement = document.getElementById('user-energy');

    if (userLevelElement) {
        userLevelElement.textContent = currentUser.level || '1';
    }

    if (userCoinsElement) {
        userCoinsElement.textContent = formatNumber(currentUser.coins || 1000);
    }

    if (userGemsElement) {
        userGemsElement.textContent = currentUser.gems || '50';
    }

    if (userEnergyElement) {
        userEnergyElement.textContent = currentUser.energy || '100';
    }
}

// 獲取用戶頭像URL
function getAvatarUrl(user) {
    console.log('🔍 檢查用戶頭像數據:', {
        user: !!user,
        discord_avatar: user?.discord_avatar,
        twitch_avatar: user?.twitch_avatar,
        avatar: user?.avatar,
        discord: user?.discord,
        twitch: user?.twitch
    });

    if (!user) {
        console.log('❌ 無用戶數據，使用默認頭像');
        return getDefaultAvatar();
    }

    // 優先順序：Discord頭像 > Twitch頭像 > 一般頭像 > 默認頭像
    if (user.discord_avatar) {
        console.log('✅ 使用Discord頭像:', user.discord_avatar);
        return user.discord_avatar;
    }

    if (user.twitch_avatar) {
        console.log('✅ 使用Twitch頭像:', user.twitch_avatar);
        return user.twitch_avatar;
    }

    if (user.avatar) {
        console.log('✅ 使用一般頭像:', user.avatar);
        return user.avatar;
    }

    // 檢查嵌套的discord/twitch物件
    if (user.discord && user.discord.avatar) {
        console.log('✅ 使用嵌套Discord頭像:', user.discord.avatar);
        return user.discord.avatar;
    }

    if (user.twitch && user.twitch.avatar) {
        console.log('✅ 使用嵌套Twitch頭像:', user.twitch.avatar);
        return user.twitch.avatar;
    }

    console.log('⚠️ 沒有找到任何頭像，使用默認頭像');
    return getDefaultAvatar();
}

// 獲取默認頭像
function getDefaultAvatar() {
    // 使用一個簡單的SVG作為默認頭像
    return 'data:image/svg+xml;base64,' + btoa(`
        <svg width="48" height="48" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
            <circle cx="24" cy="24" r="24" fill="#6366f1"/>
            <circle cx="24" cy="18" r="8" fill="white"/>
            <path d="M8 40c0-8.837 7.163-16 16-16s16 7.163 16 16" fill="white"/>
        </svg>
    `);
}

// 刷新用戶數據（獲取最新頭像）
async function refreshUserData() {
    const token = localStorage.getItem('token');
    if (!token) {
        console.log('⚠️ 沒有token，無法刷新用戶數據');
        return;
    }

    try {
        console.log('🔄 嘗試刷新用戶數據...');

        const response = await fetch('/api/auth/me', {
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            }
        });

        if (response.ok) {
            const result = await response.json();
            if (result.success && result.user) {
                console.log('✅ 獲取到最新用戶數據:', result.user);

                // 更新localStorage中的用戶數據
                const updatedUser = {
                    ...currentUser,
                    ...result.user
                };

                localStorage.setItem('user', JSON.stringify(updatedUser));
                currentUser = updatedUser;

                // 重新更新界面
                updateUserInterface();

                console.log('✅ 用戶數據刷新成功');
            }
        } else {
            console.log('⚠️ 無法獲取用戶數據，狀態碼:', response.status);
        }
    } catch (error) {
        console.log('⚠️ 刷新用戶數據失敗:', error.message);
    }
}

// 格式化數字顯示
function formatNumber(num) {
    if (num >= 1000000) {
        return (num / 1000000).toFixed(1) + 'M';
    } else if (num >= 1000) {
        return (num / 1000).toFixed(1) + 'K';
    }
    return num.toLocaleString();
}

// 購買金幣
function buyGold() {
    showModal(
        '購買金幣',
        `
        <div class="shop-content">
            <h4>選擇金幣包</h4>
            <div class="shop-items">
                <div class="shop-item" onclick="purchaseGold(1000, 10)">
                    <div class="item-icon"><i class="fas fa-coins"></i></div>
                    <div class="item-info">
                        <span class="item-amount">1,000 金幣</span>
                        <span class="item-price">10 寶石</span>
                    </div>
                </div>
                <div class="shop-item" onclick="purchaseGold(5000, 45)">
                    <div class="item-icon"><i class="fas fa-coins"></i></div>
                    <div class="item-info">
                        <span class="item-amount">5,000 金幣</span>
                        <span class="item-price">45 寶石</span>
                    </div>
                    <div class="item-badge">省 10%</div>
                </div>
                <div class="shop-item" onclick="purchaseGold(10000, 80)">
                    <div class="item-icon"><i class="fas fa-coins"></i></div>
                    <div class="item-info">
                        <span class="item-amount">10,000 金幣</span>
                        <span class="item-price">80 寶石</span>
                    </div>
                    <div class="item-badge">省 20%</div>
                </div>
            </div>
        </div>
        `,
        [
            { text: '取消', action: 'closeModal()', class: 'btn-secondary' }
        ]
    );
}

// 購買寶石
function buyGems() {
    showModal(
        '購買寶石',
        `
        <div class="shop-content">
            <h4>選擇寶石包</h4>
            <div class="shop-items">
                <div class="shop-item" onclick="purchaseGems(10, 0.99)">
                    <div class="item-icon"><i class="fas fa-gem"></i></div>
                    <div class="item-info">
                        <span class="item-amount">10 寶石</span>
                        <span class="item-price">$0.99</span>
                    </div>
                </div>
                <div class="shop-item" onclick="purchaseGems(50, 4.99)">
                    <div class="item-icon"><i class="fas fa-gem"></i></div>
                    <div class="item-info">
                        <span class="item-amount">50 寶石</span>
                        <span class="item-price">$4.99</span>
                    </div>
                </div>
                <div class="shop-item" onclick="purchaseGems(100, 9.99)">
                    <div class="item-icon"><i class="fas fa-gem"></i></div>
                    <div class="item-info">
                        <span class="item-amount">100 寶石</span>
                        <span class="item-price">$9.99</span>
                    </div>
                    <div class="item-badge">最劃算</div>
                </div>
            </div>
        </div>
        `,
        [
            { text: '取消', action: 'closeModal()', class: 'btn-secondary' }
        ]
    );
}

// 處理金幣購買
function purchaseGold(amount, cost) {
    closeModal();
    showNotification(`購買功能開發中，敬請期待！`, 'warning');
}

// 處理寶石購買
function purchaseGems(amount, price) {
    closeModal();
    showNotification(`購買功能開發中，敬請期待！`, 'warning');
}

// 新增的手遊風格功能函數

// 教學模式
function tutorial() {
    showNotification('教學模式功能開發中，敬請期待！', 'info');
}

// 顯示排行榜
function showLeaderboard() {
    showNotification('排行榜功能開發中，敬請期待！', 'info');
}



// 顯示遊戲界面
function showGameInterface() {
    setTimeout(() => {
        loadingScreen.style.opacity = '0';
        setTimeout(() => {
            loadingScreen.style.display = 'none';
            gameInterface.style.display = 'flex';

            // 顯示歡迎通知
            showNotification(`歡迎回來，${currentUser.username || currentUser.email}！`, 'success');
        }, 500);
    }, 1500);
}

// 初始化事件監聽器
function initializeEventListeners() {
    // 模態框關閉
    if (modalOverlay) {
        modalOverlay.addEventListener('click', (e) => {
            if (e.target === modalOverlay) {
                closeModal();
            }
        });
    }

    // ESC 鍵關閉模態框
    document.addEventListener('keydown', (e) => {
        if (e.key === 'Escape') {
            closeModal();
        }
    });

    // 手遊風格卡片點擊事件（如果存在）
    const gameCards = document.querySelectorAll('.game-card-large, .game-card-medium');
    gameCards.forEach(card => {
        card.addEventListener('click', () => {
            // 添加點擊效果
            card.style.transform = 'scale(0.95)';
            setTimeout(() => {
                card.style.transform = '';
            }, 150);
        });
    });
}

// 初始化導航（手遊風格版本）
function initializeNavigation() {
    // 手遊風格不需要切換區域，直接顯示主要內容
    console.log('🎮 手遊風格界面初始化完成');
}

// 切換內容區域（為了相容性保留）
function switchSection(sectionName) {
    console.log('🔄 手遊風格模式，不需要切換區域:', sectionName);
    // 手遊風格版本不需要區域切換
}

// ===== 遊戲功能函數 =====

// 快速匹配
function quickMatch() {
    showModal(
        '快速匹配',
        `
        <div style="text-align: center; padding: 20px;">
            <div class="spinner" style="margin: 0 auto 20px;"></div>
            <p>正在尋找對手...</p>
            <p style="color: #888; font-size: 0.9rem; margin-top: 10px;">預計等待時間: 30秒</p>
        </div>
        `,
        [
            { text: '取消匹配', action: 'closeModal()', class: 'btn-secondary' }
        ]
    );

    // 模擬匹配過程
    setTimeout(() => {
        closeModal();
        showNotification('匹配成功！正在進入遊戲...', 'success');
        setTimeout(() => {
            showNotification('遊戲功能開發中，敬請期待！', 'warning');
        }, 2000);
    }, 3000);
}



// 單人模式
function singlePlayer() {
    showModal(
        '單人模式',
        `
        <div style="text-align: center;">
            <h3 style="margin-bottom: 20px;">選擇難度</h3>
            <div style="display: grid; gap: 15px;">
                <button class="btn btn-secondary" onclick="startSinglePlayer('easy')" style="width: 100%;">
                    <i class="fas fa-star"></i> 簡單
                </button>
                <button class="btn btn-secondary" onclick="startSinglePlayer('normal')" style="width: 100%;">
                    <i class="fas fa-star"></i><i class="fas fa-star"></i> 普通
                </button>
                <button class="btn btn-secondary" onclick="startSinglePlayer('hard')" style="width: 100%;">
                    <i class="fas fa-star"></i><i class="fas fa-star"></i><i class="fas fa-star"></i> 困難
                </button>
                <button class="btn btn-secondary" onclick="startSinglePlayer('expert')" style="width: 100%;">
                    <i class="fas fa-star"></i><i class="fas fa-star"></i><i class="fas fa-star"></i><i class="fas fa-star"></i> 專家
                </button>
            </div>
        </div>
        `,
        [
            { text: '取消', action: 'closeModal()', class: 'btn-secondary' }
        ]
    );
}

// 開始單人遊戲
function startSinglePlayer(difficulty) {
    closeModal();
    const difficultyNames = {
        easy: '簡單',
        normal: '普通',
        hard: '困難',
        expert: '專家'
    };

    showNotification(`開始${difficultyNames[difficulty]}難度單人遊戲`, 'success');

    setTimeout(() => {
        showNotification('單人模式開發中，敬請期待！', 'warning');
    }, 2000);
}

// 教學模式
function tutorial() {
    showModal(
        '教學模式',
        `
        <div style="text-align: center;">
            <h3 style="margin-bottom: 20px;">選擇教學內容</h3>
            <div style="display: grid; gap: 15px;">
                <button class="btn btn-secondary" onclick="startTutorial('basic')" style="width: 100%;">
                    <i class="fas fa-play"></i> 基礎操作
                </button>
                <button class="btn btn-secondary" onclick="startTutorial('elements')" style="width: 100%;">
                    <i class="fas fa-fire"></i> 元素系統
                </button>
                <button class="btn btn-secondary" onclick="startTutorial('combat')" style="width: 100%;">
                    <i class="fas fa-sword"></i> 戰鬥技巧
                </button>
                <button class="btn btn-secondary" onclick="startTutorial('advanced')" style="width: 100%;">
                    <i class="fas fa-graduation-cap"></i> 進階策略
                </button>
            </div>
        </div>
        `,
        [
            { text: '取消', action: 'closeModal()', class: 'btn-secondary' }
        ]
    );
}

// 開始教學
function startTutorial(type) {
    closeModal();
    const tutorialNames = {
        basic: '基礎操作',
        elements: '元素系統',
        combat: '戰鬥技巧',
        advanced: '進階策略'
    };

    showNotification(`開始${tutorialNames[type]}教學`, 'success');

    setTimeout(() => {
        showNotification('教學模式開發中，敬請期待！', 'warning');
    }, 2000);
}



// ===== 用戶功能 =====

// 顯示設置
function showSettings() {
    showModal(
        '遊戲設置',
        `
        <div class="settings-grid">
            <div class="setting-group">
                <h4>音效設置</h4>
                <div class="setting-item">
                    <label>主音量</label>
                    <input type="range" min="0" max="100" value="80">
                </div>
                <div class="setting-item">
                    <label>音效音量</label>
                    <input type="range" min="0" max="100" value="70">
                </div>
                <div class="setting-item">
                    <label>背景音樂</label>
                    <input type="range" min="0" max="100" value="60">
                </div>
            </div>

            <div class="setting-group">
                <h4>畫面設置</h4>
                <div class="setting-item">
                    <label>畫質</label>
                    <select>
                        <option>低</option>
                        <option selected>中</option>
                        <option>高</option>
                        <option>超高</option>
                    </select>
                </div>
                <div class="setting-item">
                    <label>全螢幕</label>
                    <input type="checkbox">
                </div>
                <div class="setting-item">
                    <label>垂直同步</label>
                    <input type="checkbox" checked>
                </div>
            </div>

            <div class="setting-group">
                <h4>控制設置</h4>
                <div class="setting-item">
                    <label>滑鼠靈敏度</label>
                    <input type="range" min="1" max="10" value="5">
                </div>
                <div class="setting-item">
                    <label>鍵盤配置</label>
                    <button class="btn btn-secondary">自定義按鍵</button>
                </div>
            </div>
        </div>
        `,
        [
            { text: '取消', action: 'closeModal()', class: 'btn-secondary' },
            { text: '保存設置', action: 'saveSettings()', class: 'btn-primary' }
        ]
    );
}

// 保存設置
function saveSettings() {
    closeModal();
    showNotification('設置已保存', 'success');
}

// 顯示個人資料
function showProfile() {
    // 優先使用 Discord 頭像
    let profileAvatarUrl = './assets/images/default-avatar.png';
    if (currentUser.discord_avatar) {
        profileAvatarUrl = currentUser.discord_avatar;
    } else if (currentUser.avatar) {
        profileAvatarUrl = currentUser.avatar;
    }

    showModal(
        '個人資料',
        `
        <div class="profile-content">
            <div class="profile-header">
                <div class="profile-avatar">
                    <img src="${profileAvatarUrl}" alt="頭像" onerror="this.src='./assets/images/default-avatar.png'">
                </div>
                <div class="profile-info">
                    <h3>${currentUser.username || currentUser.email}</h3>
                    <p>等級 ${currentUser.level || 1} • ${currentUser.coins || 1000} 金幣</p>
                </div>
            </div>

            <div class="profile-stats">
                <div class="stat-item">
                    <span class="stat-label">總遊戲時間</span>
                    <span class="stat-value">12小時30分</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">勝利場次</span>
                    <span class="stat-value">25</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">總場次</span>
                    <span class="stat-value">45</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">勝率</span>
                    <span class="stat-value">55.6%</span>
                </div>
            </div>
        </div>
        `,
        [
            { text: '關閉', action: 'closeModal()', class: 'btn-secondary' },
            { text: '編輯資料', action: 'editProfile()', class: 'btn-primary' }
        ]
    );
}

// 編輯個人資料
function editProfile() {
    showNotification('個人資料編輯功能開發中', 'warning');
    closeModal();
}

// 登出
function logout() {
    showModal(
        '確認登出',
        '<p>您確定要登出嗎？</p>',
        [
            { text: '取消', action: 'closeModal()', class: 'btn-secondary' },
            { text: '確認登出', action: 'confirmLogout()', class: 'btn-danger' }
        ]
    );
}

// 確認登出
function confirmLogout() {
    localStorage.removeItem('user');
    localStorage.removeItem('token');
    showNotification('已成功登出', 'success');
    setTimeout(() => {
        window.location.href = '/';
    }, 1500);
}

// ===== 工具函數 =====

// 顯示模態框
function showModal(title, content, buttons = []) {
    document.getElementById('modal-title').textContent = title;
    document.getElementById('modal-body').innerHTML = content;

    const footer = document.getElementById('modal-footer');
    footer.innerHTML = '';

    buttons.forEach(button => {
        const btn = document.createElement('button');
        btn.className = `btn ${button.class || 'btn-secondary'}`;
        btn.textContent = button.text;
        btn.onclick = () => eval(button.action);
        footer.appendChild(btn);
    });

    modalOverlay.classList.add('active');
}

// 關閉模態框
function closeModal() {
    modalOverlay.classList.remove('active');
}

// 顯示通知
function showNotification(message, type = 'info', duration = 4000) {
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;

    const icon = {
        success: 'fas fa-check-circle',
        warning: 'fas fa-exclamation-triangle',
        error: 'fas fa-times-circle',
        info: 'fas fa-info-circle'
    }[type] || 'fas fa-info-circle';

    notification.innerHTML = `
        <div style="display: flex; align-items: center; gap: 10px;">
            <i class="${icon}"></i>
            <span>${message}</span>
        </div>
    `;

    notificationContainer.appendChild(notification);

    // 自動移除通知
    setTimeout(() => {
        notification.style.animation = 'notificationSlideOut 0.3s ease forwards';
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 300);
    }, duration);
}

// 添加滑出動畫
const style = document.createElement('style');
style.textContent = `
    @keyframes notificationSlideOut {
        to {
            opacity: 0;
            transform: translateX(100%);
        }
    }

    .form-group {
        margin-bottom: 20px;
    }

    .form-group label {
        display: block;
        margin-bottom: 8px;
        color: var(--text-primary);
        font-weight: 500;
    }

    .form-group input,
    .form-group select {
        width: 100%;
        padding: 10px 12px;
        border: 1px solid var(--border-color);
        border-radius: 6px;
        background: var(--bg-tertiary);
        color: var(--text-primary);
        font-size: 14px;
    }

    .form-group input:focus,
    .form-group select:focus {
        outline: none;
        border-color: var(--primary-color);
    }

    .settings-grid {
        display: grid;
        gap: 25px;
    }

    .setting-group h4 {
        color: var(--primary-color);
        margin-bottom: 15px;
        font-family: var(--font-gaming);
    }

    .setting-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 15px;
    }

    .setting-item label {
        margin-bottom: 0;
        flex: 1;
    }

    .setting-item input,
    .setting-item select {
        width: auto;
        min-width: 120px;
    }

    .profile-content {
        text-align: center;
    }

    .profile-header {
        margin-bottom: 25px;
    }

    .profile-avatar {
        width: 80px;
        height: 80px;
        border-radius: 50%;
        overflow: hidden;
        margin: 0 auto 15px;
        border: 3px solid var(--primary-color);
    }

    .profile-avatar img {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }

    .profile-info h3 {
        color: var(--text-primary);
        margin-bottom: 5px;
        font-family: var(--font-gaming);
    }

    .profile-info p {
        color: var(--text-secondary);
    }

    .profile-stats {
        display: grid;
        gap: 15px;
    }

    .stat-item {
        display: flex;
        justify-content: space-between;
        padding: 10px 0;
        border-bottom: 1px solid var(--border-color);
    }

    .stat-label {
        color: var(--text-secondary);
    }

    .stat-value {
        color: var(--text-primary);
        font-weight: 600;
    }

    .btn-danger {
        background: var(--danger-color);
        color: white;
    }

    .btn-danger:hover {
        background: #d32f2f;
    }
`;
document.head.appendChild(style);

// ===== 頁面初始化 =====

// 當頁面載入完成時初始化
document.addEventListener('DOMContentLoaded', function() {
    console.log('🎮 開始初始化遊戲界面...');

    // 初始化 DOM 元素
    loadingScreen = document.getElementById('loading-screen');
    gameInterface = document.getElementById('game-interface');
    modalOverlay = document.getElementById('modal-overlay');
    notificationContainer = document.getElementById('notification-container');

    // 檢查用戶登入狀態
    checkAuthStatus();

    // 初始化事件監聽器
    initializeEventListeners();

    // 初始化導航
    initializeNavigation();

    console.log('✅ 遊戲界面初始化完成');
});
