/**
 * P1P2方塊大亂鬥 - 房間管理系統
 * 負責管理多個遊戲房間和玩家分配
 */

class RoomManager {
    constructor() {
        this.rooms = new Map(); // 所有房間
        this.playerRoomMap = new Map(); // 玩家到房間的映射
        this.roomTypes = {
            'classic_duel': { maxPlayers: 2, minPlayers: 2 },
            'team_battle': { maxPlayers: 4, minPlayers: 4 },
            'free_for_all': { maxPlayers: 8, minPlayers: 3 },
            'tournament': { maxPlayers: 16, minPlayers: 8 }
        };

        this.roomSettings = {
            maxRooms: 200,
            roomTimeout: 30 * 60 * 1000, // 30分鐘
            cleanupInterval: 60 * 1000 // 1分鐘清理一次
        };

        this.init();
    }

    init() {
        console.log('🏠 房間管理系統初始化...');

        // 設置房間清理
        this.setupRoomCleanup();

        // 設置房間監控
        this.setupRoomMonitoring();

        console.log('✅ 房間管理系統已啟動');
    }

    // 創建房間
    createRoom(roomType, hostPlayerId, options = {}) {
        if (this.rooms.size >= this.roomSettings.maxRooms) {
            throw new Error('服務器房間已滿');
        }

        const roomConfig = this.roomTypes[roomType];
        if (!roomConfig) {
            throw new Error('無效的房間類型');
        }

        const roomId = this.generateRoomId();
        const room = {
            id: roomId,
            type: roomType,
            hostId: hostPlayerId,
            players: new Map(),
            spectators: new Map(),
            status: 'waiting', // waiting, starting, in_progress, finished
            settings: {
                ...roomConfig,
                timeLimit: options.timeLimit || 180,
                arena: options.arena || 'standard',
                private: options.private || false,
                password: options.password || null
            },
            gameState: {
                startTime: null,
                endTime: null,
                winner: null,
                statistics: {}
            },
            createdAt: Date.now(),
            lastActivity: Date.now()
        };

        // 添加房主到房間
        this.addPlayerToRoom(roomId, hostPlayerId, { isHost: true });

        this.rooms.set(roomId, room);

        console.log(`🏠 創建房間: ${roomId} (${roomType})`);
        return room;
    }

    // 加入房間
    joinRoom(roomId, playerId, options = {}) {
        const room = this.rooms.get(roomId);
        if (!room) {
            throw new Error('房間不存在');
        }

        // 檢查房間狀態
        if (room.status !== 'waiting') {
            throw new Error('房間已開始或已結束');
        }

        // 檢查密碼
        if (room.settings.private && room.settings.password !== options.password) {
            throw new Error('房間密碼錯誤');
        }

        // 檢查房間容量
        if (room.players.size >= room.settings.maxPlayers) {
            // 嘗試加入觀戰
            if (options.spectate) {
                return this.addSpectatorToRoom(roomId, playerId);
            }
            throw new Error('房間已滿');
        }

        // 檢查玩家是否已在其他房間
        if (this.playerRoomMap.has(playerId)) {
            const currentRoomId = this.playerRoomMap.get(playerId);
            if (currentRoomId !== roomId) {
                this.leaveRoom(currentRoomId, playerId);
            }
        }

        this.addPlayerToRoom(roomId, playerId, options);

        console.log(`👤 玩家 ${playerId} 加入房間 ${roomId}`);
        return room;
    }

    // 添加玩家到房間
    addPlayerToRoom(roomId, playerId, options = {}) {
        const room = this.rooms.get(roomId);
        const player = {
            id: playerId,
            joinedAt: Date.now(),
            isReady: false,
            isHost: options.isHost || false,
            character: options.character || null,
            element: options.element || null,
            position: options.position || null
        };

        room.players.set(playerId, player);
        this.playerRoomMap.set(playerId, roomId);
        room.lastActivity = Date.now();

        // 通知房間內其他玩家
        this.broadcastToRoom(roomId, {
            type: 'player_joined',
            data: {
                playerId,
                player,
                roomInfo: this.getRoomInfo(roomId)
            }
        }, playerId);

        return player;
    }

    // 添加觀戰者
    addSpectatorToRoom(roomId, playerId) {
        const room = this.rooms.get(roomId);
        const spectator = {
            id: playerId,
            joinedAt: Date.now()
        };

        room.spectators.set(playerId, spectator);
        this.playerRoomMap.set(playerId, roomId);
        room.lastActivity = Date.now();

        console.log(`👁️ 觀戰者 ${playerId} 加入房間 ${roomId}`);
        return spectator;
    }

    // 離開房間
    leaveRoom(roomId, playerId) {
        const room = this.rooms.get(roomId);
        if (!room) return;

        const wasPlayer = room.players.has(playerId);
        const wasSpectator = room.spectators.has(playerId);

        if (wasPlayer) {
            const player = room.players.get(playerId);
            room.players.delete(playerId);

            // 如果是房主離開，轉移房主權限
            if (player.isHost && room.players.size > 0) {
                this.transferHost(roomId);
            }

            // 通知其他玩家
            this.broadcastToRoom(roomId, {
                type: 'player_left',
                data: {
                    playerId,
                    roomInfo: this.getRoomInfo(roomId)
                }
            });
        }

        if (wasSpectator) {
            room.spectators.delete(playerId);
        }

        this.playerRoomMap.delete(playerId);
        room.lastActivity = Date.now();

        // 如果房間空了，標記為清理
        if (room.players.size === 0) {
            room.status = 'empty';
        }

        console.log(`👋 玩家 ${playerId} 離開房間 ${roomId}`);
    }

    // 轉移房主
    transferHost(roomId) {
        const room = this.rooms.get(roomId);
        if (!room || room.players.size === 0) return;

        // 選擇第一個玩家作為新房主
        const newHostId = room.players.keys().next().value;
        const newHost = room.players.get(newHostId);
        newHost.isHost = true;
        room.hostId = newHostId;

        this.broadcastToRoom(roomId, {
            type: 'host_changed',
            data: {
                newHostId,
                roomInfo: this.getRoomInfo(roomId)
            }
        });

        console.log(`👑 房主轉移: ${roomId} -> ${newHostId}`);
    }

    // 設置玩家準備狀態
    setPlayerReady(roomId, playerId, ready) {
        const room = this.rooms.get(roomId);
        if (!room) return false;

        const player = room.players.get(playerId);
        if (!player) return false;

        player.isReady = ready;
        room.lastActivity = Date.now();

        // 通知房間內所有玩家
        this.broadcastToRoom(roomId, {
            type: 'player_ready_changed',
            data: {
                playerId,
                ready,
                roomInfo: this.getRoomInfo(roomId)
            }
        });

        // 檢查是否所有玩家都準備好了
        this.checkRoomReadyStatus(roomId);

        return true;
    }

    // 檢查房間準備狀態
    checkRoomReadyStatus(roomId) {
        const room = this.rooms.get(roomId);
        if (!room || room.status !== 'waiting') return;

        const playerCount = room.players.size;
        const readyCount = Array.from(room.players.values())
            .filter(player => player.isReady).length;

        // 檢查是否達到最小玩家數且所有玩家都準備好了
        if (playerCount >= room.settings.minPlayers && readyCount === playerCount) {
            this.startRoom(roomId);
        }
    }

    // 開始房間遊戲
    startRoom(roomId) {
        const room = this.rooms.get(roomId);
        if (!room || room.status !== 'waiting') return;

        room.status = 'starting';
        room.gameState.startTime = Date.now();

        // 通知所有玩家遊戲開始
        this.broadcastToRoom(roomId, {
            type: 'game_starting',
            data: {
                roomId,
                countdown: 3,
                roomInfo: this.getRoomInfo(roomId)
            }
        });

        // 3秒倒計時後正式開始
        setTimeout(() => {
            this.actuallyStartGame(roomId);
        }, 3000);

        console.log(`🚀 房間 ${roomId} 開始遊戲`);
    }

    // 實際開始遊戲
    actuallyStartGame(roomId) {
        const room = this.rooms.get(roomId);
        if (!room) return;

        room.status = 'in_progress';

        // 為每個玩家分配遊戲數據
        const gameData = this.generateGameData(room);

        this.broadcastToRoom(roomId, {
            type: 'game_started',
            data: {
                roomId,
                gameData,
                battleUrl: `/battle?room=${roomId}`
            }
        });

        console.log(`⚔️ 房間 ${roomId} 遊戲正式開始`);
    }

    // 生成遊戲數據
    generateGameData(room) {
        const players = Array.from(room.players.values());

        return {
            roomId: room.id,
            gameMode: room.type,
            players: players.map((player, index) => ({
                id: player.id,
                position: index,
                element: player.element || this.getRandomElement(),
                startPosition: this.getStartPosition(index, players.length)
            })),
            settings: room.settings,
            arena: room.settings.arena
        };
    }

    // 結束房間遊戲
    endRoom(roomId, result) {
        const room = this.rooms.get(roomId);
        if (!room) return;

        room.status = 'finished';
        room.gameState.endTime = Date.now();
        room.gameState.winner = result.winner;
        room.gameState.statistics = result.statistics;

        this.broadcastToRoom(roomId, {
            type: 'game_ended',
            data: {
                roomId,
                result,
                duration: room.gameState.endTime - room.gameState.startTime
            }
        });

        // 5分鐘後清理房間
        setTimeout(() => {
            this.cleanupRoom(roomId);
        }, 5 * 60 * 1000);

        console.log(`🏁 房間 ${roomId} 遊戲結束`);
    }

    // 廣播消息到房間
    broadcastToRoom(roomId, message, excludePlayerId = null) {
        const room = this.rooms.get(roomId);
        if (!room) return;

        // 發送給玩家
        room.players.forEach((player, playerId) => {
            if (playerId !== excludePlayerId) {
                this.sendMessageToPlayer(playerId, message);
            }
        });

        // 發送給觀戰者
        room.spectators.forEach((spectator, spectatorId) => {
            if (spectatorId !== excludePlayerId) {
                this.sendMessageToPlayer(spectatorId, message);
            }
        });
    }

    // 發送消息給玩家
    sendMessageToPlayer(playerId, message) {
        // 這裡需要與WebSocket系統整合
        if (window.gameMatch && window.gameMatch.socket) {
            try {
                window.gameMatch.socket.send(JSON.stringify({
                    type: 'room_message',
                    targetPlayer: playerId,
                    message: message
                }));
            } catch (error) {
                console.error('發送消息失敗:', error);
            }
        }
    }

    // 獲取房間信息
    getRoomInfo(roomId) {
        const room = this.rooms.get(roomId);
        if (!room) return null;

        return {
            id: room.id,
            type: room.type,
            status: room.status,
            hostId: room.hostId,
            playerCount: room.players.size,
            maxPlayers: room.settings.maxPlayers,
            spectatorCount: room.spectators.size,
            settings: room.settings,
            players: Array.from(room.players.values()),
            createdAt: room.createdAt
        };
    }

    // 獲取房間列表
    getRoomList(filters = {}) {
        const rooms = Array.from(this.rooms.values())
            .filter(room => {
                if (filters.type && room.type !== filters.type) return false;
                if (filters.status && room.status !== filters.status) return false;
                if (filters.hasSpace && room.players.size >= room.settings.maxPlayers) return false;
                if (filters.notPrivate && room.settings.private) return false;
                return true;
            })
            .map(room => this.getRoomInfo(room.id))
            .sort((a, b) => b.createdAt - a.createdAt);

        return rooms;
    }

    // 房間清理
    setupRoomCleanup() {
        setInterval(() => {
            this.cleanupExpiredRooms();
        }, this.roomSettings.cleanupInterval);
    }

    // 清理過期房間
    cleanupExpiredRooms() {
        const now = Date.now();
        const expiredRooms = [];

        this.rooms.forEach((room, roomId) => {
            const isExpired = now - room.lastActivity > this.roomSettings.roomTimeout;
            const isEmpty = room.status === 'empty';
            const isFinished = room.status === 'finished' &&
                now - room.gameState.endTime > 5 * 60 * 1000; // 結束後5分鐘

            if (isExpired || isEmpty || isFinished) {
                expiredRooms.push(roomId);
            }
        });

        expiredRooms.forEach(roomId => {
            this.cleanupRoom(roomId);
        });

        if (expiredRooms.length > 0) {
            console.log(`🧹 清理了 ${expiredRooms.length} 個過期房間`);
        }
    }

    // 清理單個房間
    cleanupRoom(roomId) {
        const room = this.rooms.get(roomId);
        if (!room) return;

        // 移除所有玩家的房間映射
        room.players.forEach((player, playerId) => {
            this.playerRoomMap.delete(playerId);
        });

        room.spectators.forEach((spectator, spectatorId) => {
            this.playerRoomMap.delete(spectatorId);
        });

        // 移除房間
        this.rooms.delete(roomId);

        console.log(`🗑️ 清理房間: ${roomId}`);
    }

    // 房間監控
    setupRoomMonitoring() {
        setInterval(() => {
            const stats = this.getRoomStatistics();
            if (stats.totalRooms > 100) {
                console.log('📊 房間統計:', stats);
            }
        }, 30000); // 每30秒記錄一次
    }

    // 獲取房間統計
    getRoomStatistics() {
        const stats = {
            totalRooms: this.rooms.size,
            activeRooms: 0,
            waitingRooms: 0,
            totalPlayers: 0,
            totalSpectators: 0,
            roomsByType: {}
        };

        this.rooms.forEach(room => {
            if (room.status === 'in_progress') stats.activeRooms++;
            if (room.status === 'waiting') stats.waitingRooms++;

            stats.totalPlayers += room.players.size;
            stats.totalSpectators += room.spectators.size;

            stats.roomsByType[room.type] = (stats.roomsByType[room.type] || 0) + 1;
        });

        return stats;
    }

    // 工具方法
    generateRoomId() {
        return 'room_' + Date.now() + '_' + Math.random().toString(36).substr(2, 6);
    }

    getRandomElement() {
        const elements = ['fire', 'water', 'earth', 'air', 'electric'];
        return elements[Math.floor(Math.random() * elements.length)];
    }

    getStartPosition(index, totalPlayers) {
        // 根據玩家數量和索引計算起始位置
        const angle = (index / totalPlayers) * 2 * Math.PI;
        const radius = 300;
        return {
            x: 600 + Math.cos(angle) * radius,
            y: 400 + Math.sin(angle) * radius
        };
    }
}

// 創建全局實例（避免重複創建）
if (!window.roomManager) {
    window.roomManager = new RoomManager();
}

// 添加全局調試命令
window.debugRooms = {
    list: () => window.roomManager.getRoomList(),
    stats: () => window.roomManager.getRoomStatistics(),
    info: (roomId) => window.roomManager.getRoomInfo(roomId),
    cleanup: () => window.roomManager.cleanupExpiredRooms()
};

// 導出供其他模組使用
if (typeof module !== 'undefined' && module.exports) {
    module.exports = RoomManager;
}
