/**
 * P1P2方塊大亂鬥 - 連接穩定性管理器
 * 負責管理WebSocket連接、自動重連、降級處理
 */

class ConnectionManager {
    constructor() {
        this.connections = new Map();
        this.reconnectStrategies = new Map();
        this.isOnline = navigator.onLine;
        this.heartbeatInterval = 30000; // 30秒心跳
        this.maxReconnectAttempts = 5;

        this.init();
    }

    init() {
        console.log('🔗 連接管理器初始化...');

        // 監聽網絡狀態變化
        this.setupNetworkMonitoring();

        // 設置心跳檢測
        this.setupHeartbeat();

        console.log('✅ 連接管理器已啟動');
    }

    // 網絡監控
    setupNetworkMonitoring() {
        window.addEventListener('online', () => {
            console.log('🌐 網絡連接已恢復');
            this.isOnline = true;
            this.handleNetworkRestore();
        });

        window.addEventListener('offline', () => {
            console.log('📡 網絡連接中斷');
            this.isOnline = false;
            this.handleNetworkLoss();
        });
    }

    // 心跳檢測
    setupHeartbeat() {
        setInterval(() => {
            this.performHeartbeat();
        }, this.heartbeatInterval);
    }

    // 註冊連接
    registerConnection(name, socket, options = {}) {
        const connection = {
            name,
            socket,
            status: 'connected',
            lastHeartbeat: Date.now(),
            reconnectAttempts: 0,
            options: {
                autoReconnect: true,
                maxReconnectAttempts: this.maxReconnectAttempts,
                reconnectDelay: 3000,
                ...options
            }
        };

        this.connections.set(name, connection);
        this.setupConnectionHandlers(connection);

        console.log(`📝 已註冊連接: ${name}`);
        return connection;
    }

    // 設置連接處理器
    setupConnectionHandlers(connection) {
        const { socket } = connection;

        socket.addEventListener('open', () => {
            connection.status = 'connected';
            connection.reconnectAttempts = 0;
            connection.lastHeartbeat = Date.now();
            console.log(`✅ 連接已建立: ${connection.name}`);
        });

        socket.addEventListener('close', () => {
            connection.status = 'disconnected';
            console.log(`❌ 連接已關閉: ${connection.name}`);
            this.handleConnectionLoss(connection);
        });

        socket.addEventListener('error', (error) => {
            connection.status = 'error';
            console.error(`🚨 連接錯誤 [${connection.name}]:`, error);
            this.handleConnectionError(connection, error);
        });

        socket.addEventListener('message', () => {
            connection.lastHeartbeat = Date.now();
        });
    }

    // 處理連接丟失
    handleConnectionLoss(connection) {
        if (connection.options.autoReconnect && this.isOnline) {
            this.scheduleReconnect(connection);
        } else {
            this.enableFallbackMode(connection);
        }
    }

    // 處理連接錯誤
    handleConnectionError(connection, error) {
        // 記錄錯誤
        if (window.systemMonitor) {
            window.systemMonitor.recordError('connection', {
                name: connection.name,
                error: error.message || 'Unknown error'
            });
        }

        // 嘗試重連
        this.handleConnectionLoss(connection);
    }

    // 安排重連
    scheduleReconnect(connection) {
        if (connection.reconnectAttempts >= connection.options.maxReconnectAttempts) {
            console.log(`❌ 連接 ${connection.name} 重連次數已達上限`);
            this.enableFallbackMode(connection);
            return;
        }

        connection.reconnectAttempts++;
        const delay = connection.options.reconnectDelay * connection.reconnectAttempts;

        console.log(`🔄 將在 ${delay}ms 後重連 ${connection.name} (第${connection.reconnectAttempts}次)`);

        setTimeout(() => {
            this.attemptReconnect(connection);
        }, delay);
    }

    // 嘗試重連
    attemptReconnect(connection) {
        if (!this.isOnline) {
            console.log(`⚠️ 網絡離線，跳過重連 ${connection.name}`);
            return;
        }

        console.log(`🔄 嘗試重連 ${connection.name}...`);

        try {
            // 根據連接類型執行重連邏輯
            this.executeReconnectStrategy(connection);
        } catch (error) {
            console.error(`❌ 重連失敗 [${connection.name}]:`, error);
            this.scheduleReconnect(connection);
        }
    }

    // 執行重連策略
    executeReconnectStrategy(connection) {
        const strategy = this.reconnectStrategies.get(connection.name);

        if (strategy) {
            strategy(connection);
        } else {
            // 默認重連策略
            this.defaultReconnectStrategy(connection);
        }
    }

    // 默認重連策略
    defaultReconnectStrategy(connection) {
        // 創建新的WebSocket連接
        const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
        const wsUrl = `${protocol}//${window.location.host}/ws`;

        const newSocket = new WebSocket(wsUrl);
        connection.socket = newSocket;
        this.setupConnectionHandlers(connection);
    }

    // 註冊重連策略
    registerReconnectStrategy(connectionName, strategy) {
        this.reconnectStrategies.set(connectionName, strategy);
        console.log(`📋 已註冊重連策略: ${connectionName}`);
    }

    // 啟用降級模式
    enableFallbackMode(connection) {
        console.log(`🔄 為 ${connection.name} 啟用降級模式`);
        connection.status = 'fallback';

        // 通知相關系統進入降級模式
        this.notifyFallbackMode(connection);
    }

    // 通知降級模式
    notifyFallbackMode(connection) {
        const event = new CustomEvent('connectionFallback', {
            detail: { connection: connection.name }
        });
        window.dispatchEvent(event);
    }

    // 執行心跳檢測
    performHeartbeat() {
        const now = Date.now();

        this.connections.forEach((connection, name) => {
            if (connection.status === 'connected') {
                // 檢查是否超時
                if (now - connection.lastHeartbeat > this.heartbeatInterval * 2) {
                    console.warn(`⚠️ 連接 ${name} 心跳超時`);
                    this.handleConnectionTimeout(connection);
                } else {
                    // 發送心跳包
                    this.sendHeartbeat(connection);
                }
            }
        });
    }

    // 發送心跳包
    sendHeartbeat(connection) {
        if (connection.socket && connection.socket.readyState === WebSocket.OPEN) {
            try {
                connection.socket.send(JSON.stringify({ type: 'ping' }));
            } catch (error) {
                console.error(`❌ 心跳發送失敗 [${connection.name}]:`, error);
                this.handleConnectionError(connection, error);
            }
        }
    }

    // 處理連接超時
    handleConnectionTimeout(connection) {
        console.log(`⏰ 連接 ${connection.name} 超時`);
        connection.status = 'timeout';
        this.handleConnectionLoss(connection);
    }

    // 處理網絡恢復
    handleNetworkRestore() {
        console.log('🔄 網絡恢復，檢查所有連接...');

        this.connections.forEach((connection, name) => {
            if (connection.status !== 'connected') {
                console.log(`🔄 嘗試恢復連接: ${name}`);
                connection.reconnectAttempts = 0; // 重置重連次數
                this.attemptReconnect(connection);
            }
        });
    }

    // 處理網絡丟失
    handleNetworkLoss() {
        console.log('📡 網絡中斷，所有連接進入等待狀態');

        this.connections.forEach((connection, name) => {
            if (connection.status === 'connected') {
                connection.status = 'waiting';
                console.log(`⏸️ 連接 ${name} 進入等待狀態`);
            }
        });
    }

    // 獲取連接狀態
    getConnectionStatus(name) {
        const connection = this.connections.get(name);
        return connection ? connection.status : 'not_found';
    }

    // 獲取所有連接狀態
    getAllConnectionStatus() {
        const status = {};
        this.connections.forEach((connection, name) => {
            status[name] = {
                status: connection.status,
                reconnectAttempts: connection.reconnectAttempts,
                lastHeartbeat: connection.lastHeartbeat
            };
        });
        return status;
    }

    // 強制重連
    forceReconnect(name) {
        const connection = this.connections.get(name);
        if (connection) {
            connection.reconnectAttempts = 0;
            this.attemptReconnect(connection);
        }
    }

    // 移除連接
    removeConnection(name) {
        const connection = this.connections.get(name);
        if (connection && connection.socket) {
            connection.socket.close();
        }
        this.connections.delete(name);
        console.log(`🗑️ 已移除連接: ${name}`);
    }

    // 清理所有連接
    cleanup() {
        console.log('🧹 清理所有連接...');
        this.connections.forEach((connection, name) => {
            this.removeConnection(name);
        });
    }
}

// 創建全局實例（避免重複創建）
if (!window.connectionManager) {
    window.connectionManager = new ConnectionManager();
}

// 導出供其他模組使用
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ConnectionManager;
}
