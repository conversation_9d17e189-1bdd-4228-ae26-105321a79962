/**
 * P1P2方塊大亂鬥 - 用戶經濟系統API
 * 包含金錢、鑽石、等級、經驗值、成就等功能
 */

const jwt = require('jsonwebtoken');
const { ObjectId } = require('mongodb');
const database = require('./database');

const JWT_SECRET = process.env.JWT_SECRET || 'p1p2-game-secret-key-2024';

// 等級系統配置
const LEVEL_CONFIG = {
    // 每級所需經驗值（指數增長）
    getRequiredExp: (level) => {
        return Math.floor(100 * Math.pow(1.5, level - 1));
    },

    // 最大等級
    MAX_LEVEL: 100,

    // 升級獎勵
    getLevelUpReward: (level) => {
        return {
            coins: level * 100,
            diamonds: Math.floor(level / 5) + 1
        };
    }
};

// 每日獎勵配置
const DAILY_REWARDS = [
    { coins: 100, diamonds: 0 },    // 第1天
    { coins: 150, diamonds: 0 },    // 第2天
    { coins: 200, diamonds: 1 },    // 第3天
    { coins: 250, diamonds: 0 },    // 第4天
    { coins: 300, diamonds: 1 },    // 第5天
    { coins: 400, diamonds: 2 },    // 第6天
    { coins: 500, diamonds: 5 }     // 第7天（大獎）
];

// 驗證JWT令牌
function verifyToken(req) {
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
        throw new Error('未提供認證令牌');
    }

    const token = authHeader.substring(7);
    const decoded = jwt.verify(token, JWT_SECRET);
    return decoded.userId || decoded.id;
}

// 計算等級和經驗值
function calculateLevel(experience) {
    let level = 1;
    let totalExpNeeded = 0;

    while (level < LEVEL_CONFIG.MAX_LEVEL) {
        const expForNextLevel = LEVEL_CONFIG.getRequiredExp(level);
        if (totalExpNeeded + expForNextLevel > experience) {
            break;
        }
        totalExpNeeded += expForNextLevel;
        level++;
    }

    const currentLevelExp = experience - totalExpNeeded;
    const nextLevelExp = level < LEVEL_CONFIG.MAX_LEVEL ? LEVEL_CONFIG.getRequiredExp(level) : 0;

    return {
        level,
        currentExp: currentLevelExp,
        nextLevelExp,
        totalExp: experience,
        progress: nextLevelExp > 0 ? (currentLevelExp / nextLevelExp * 100).toFixed(1) : 100
    };
}

// 設置用戶經濟API路由
function setupUserEconomyRoutes(app) {

    // 獲取用戶經濟數據
    app.get('/api/user/economy', async (req, res) => {
        try {
            const userId = verifyToken(req);

            await database.connect();
            const users = database.getUsersCollection();

            const user = await users.findOne({ _id: new ObjectId(userId) });
            if (!user) {
                res.writeHead(404, { 'Content-Type': 'application/json' });
                res.end(JSON.stringify({
                    success: false,
                    message: '用戶不存在'
                }));
                return;
            }

            const levelInfo = calculateLevel(user.experience || 0);

            res.writeHead(200, { 'Content-Type': 'application/json' });
            res.end(JSON.stringify({
                success: true,
                data: {
                    // 基本信息
                    userId: user._id,
                    username: user.username,

                    // 經濟數據
                    coins: user.coins || 1000,
                    diamonds: user.diamonds || 50,

                    // 等級系統
                    level: levelInfo.level,
                    experience: levelInfo.totalExp,
                    currentLevelExp: levelInfo.currentExp,
                    nextLevelExp: levelInfo.nextLevelExp,
                    levelProgress: levelInfo.progress,

                    // 遊戲統計
                    wins: user.wins || 0,
                    losses: user.losses || 0,
                    totalGames: (user.wins || 0) + (user.losses || 0),
                    winRate: (user.wins || 0) > 0 ? ((user.wins || 0) / ((user.wins || 0) + (user.losses || 0)) * 100).toFixed(1) : '0.0',

                    // 排名數據
                    rating: user.rating || 1000,
                    tier: user.tier || 'bronze',

                    // 成就和解鎖
                    achievements: user.achievements || [],
                    unlockedElements: user.unlockedElements || ['fire', 'water', 'earth', 'air'],

                    // 每日獎勵狀態
                    dailyRewards: user.dailyRewards || {
                        lastClaimDate: null,
                        consecutiveDays: 0
                    }
                }
            }));

        } catch (error) {
            console.error('❌ 獲取用戶經濟數據失敗:', error.message);
            res.writeHead(500, { 'Content-Type': 'application/json' });
            res.end(JSON.stringify({
                success: false,
                message: '獲取數據失敗'
            }));
        }
    });

    // 添加經驗值
    app.post('/api/user/add-experience', async (req, res) => {
        try {
            const userId = verifyToken(req);
            const { amount, reason } = req.body;

            if (!amount || amount <= 0) {
                res.writeHead(400, { 'Content-Type': 'application/json' });
                res.end(JSON.stringify({
                    success: false,
                    message: '經驗值數量無效'
                }));
                return;
            }

            await database.connect();
            const users = database.getUsersCollection();

            const user = await users.findOne({ _id: new ObjectId(userId) });
            if (!user) {
                res.writeHead(404, { 'Content-Type': 'application/json' });
                res.end(JSON.stringify({
                    success: false,
                    message: '用戶不存在'
                }));
                return;
            }

            const oldExp = user.experience || 0;
            const newExp = oldExp + amount;

            const oldLevelInfo = calculateLevel(oldExp);
            const newLevelInfo = calculateLevel(newExp);

            const leveledUp = newLevelInfo.level > oldLevelInfo.level;
            let rewards = { coins: 0, diamonds: 0 };

            // 如果升級了，給予升級獎勵
            if (leveledUp) {
                for (let level = oldLevelInfo.level + 1; level <= newLevelInfo.level; level++) {
                    const levelReward = LEVEL_CONFIG.getLevelUpReward(level);
                    rewards.coins += levelReward.coins;
                    rewards.diamonds += levelReward.diamonds;
                }
            }

            // 更新用戶數據
            const updateData = {
                experience: newExp,
                level: newLevelInfo.level,
                lastActiveAt: new Date()
            };

            if (rewards.coins > 0) {
                updateData.coins = (user.coins || 1000) + rewards.coins;
            }
            if (rewards.diamonds > 0) {
                updateData.diamonds = (user.diamonds || 50) + rewards.diamonds;
            }

            await users.updateOne(
                { _id: new ObjectId(userId) },
                { $set: updateData }
            );

            res.writeHead(200, { 'Content-Type': 'application/json' });
            res.end(JSON.stringify({
                success: true,
                data: {
                    experienceAdded: amount,
                    newExperience: newExp,
                    leveledUp,
                    oldLevel: oldLevelInfo.level,
                    newLevel: newLevelInfo.level,
                    rewards,
                    reason: reason || '遊戲獎勵'
                }
            }));

        } catch (error) {
            console.error('❌ 添加經驗值失敗:', error.message);
            res.writeHead(500, { 'Content-Type': 'application/json' });
            res.end(JSON.stringify({
                success: false,
                message: '添加經驗值失敗'
            }));
        }
    });

    // 添加金錢
    app.post('/api/user/add-coins', async (req, res) => {
        try {
            const userId = verifyToken(req);
            const { amount, reason } = req.body;

            if (!amount || amount <= 0) {
                res.writeHead(400, { 'Content-Type': 'application/json' });
                res.end(JSON.stringify({
                    success: false,
                    message: '金錢數量無效'
                }));
                return;
            }

            await database.connect();
            const users = database.getUsersCollection();

            const user = await users.findOne({ _id: new ObjectId(userId) });
            if (!user) {
                res.writeHead(404, { 'Content-Type': 'application/json' });
                res.end(JSON.stringify({
                    success: false,
                    message: '用戶不存在'
                }));
                return;
            }

            const newCoins = (user.coins || 1000) + amount;

            await users.updateOne(
                { _id: new ObjectId(userId) },
                {
                    $set: {
                        coins: newCoins,
                        lastActiveAt: new Date()
                    }
                }
            );

            res.writeHead(200, { 'Content-Type': 'application/json' });
            res.end(JSON.stringify({
                success: true,
                data: {
                    coinsAdded: amount,
                    newCoins,
                    reason: reason || '遊戲獎勵'
                }
            }));

        } catch (error) {
            console.error('❌ 添加金錢失敗:', error.message);
            res.writeHead(500, { 'Content-Type': 'application/json' });
            res.end(JSON.stringify({
                success: false,
                message: '添加金錢失敗'
            }));
        }
    });

    // 添加鑽石
    app.post('/api/user/add-diamonds', async (req, res) => {
        try {
            const userId = verifyToken(req);
            const { amount, reason } = req.body;

            if (!amount || amount <= 0) {
                res.writeHead(400, { 'Content-Type': 'application/json' });
                res.end(JSON.stringify({
                    success: false,
                    message: '鑽石數量無效'
                }));
                return;
            }

            await database.connect();
            const users = database.getUsersCollection();

            const user = await users.findOne({ _id: new ObjectId(userId) });
            if (!user) {
                res.writeHead(404, { 'Content-Type': 'application/json' });
                res.end(JSON.stringify({
                    success: false,
                    message: '用戶不存在'
                }));
                return;
            }

            const newDiamonds = (user.diamonds || 50) + amount;

            await users.updateOne(
                { _id: new ObjectId(userId) },
                {
                    $set: {
                        diamonds: newDiamonds,
                        lastActiveAt: new Date()
                    }
                }
            );

            res.writeHead(200, { 'Content-Type': 'application/json' });
            res.end(JSON.stringify({
                success: true,
                data: {
                    diamondsAdded: amount,
                    newDiamonds,
                    reason: reason || '遊戲獎勵'
                }
            }));

        } catch (error) {
            console.error('❌ 添加鑽石失敗:', error.message);
            res.writeHead(500, { 'Content-Type': 'application/json' });
            res.end(JSON.stringify({
                success: false,
                message: '添加鑽石失敗'
            }));
        }
    });

    // 消費金錢
    app.post('/api/user/spend-coins', async (req, res) => {
        try {
            const userId = verifyToken(req);
            const { amount, reason } = req.body;

            if (!amount || amount <= 0) {
                res.writeHead(400, { 'Content-Type': 'application/json' });
                res.end(JSON.stringify({
                    success: false,
                    message: '消費金額無效'
                }));
                return;
            }

            await database.connect();
            const users = database.getUsersCollection();

            const user = await users.findOne({ _id: new ObjectId(userId) });
            if (!user) {
                res.writeHead(404, { 'Content-Type': 'application/json' });
                res.end(JSON.stringify({
                    success: false,
                    message: '用戶不存在'
                }));
                return;
            }

            const currentCoins = user.coins || 1000;
            if (currentCoins < amount) {
                res.writeHead(400, { 'Content-Type': 'application/json' });
                res.end(JSON.stringify({
                    success: false,
                    message: '金錢不足',
                    data: {
                        required: amount,
                        current: currentCoins,
                        shortage: amount - currentCoins
                    }
                }));
                return;
            }

            const newCoins = currentCoins - amount;

            await users.updateOne(
                { _id: new ObjectId(userId) },
                {
                    $set: {
                        coins: newCoins,
                        lastActiveAt: new Date()
                    }
                }
            );

            res.writeHead(200, { 'Content-Type': 'application/json' });
            res.end(JSON.stringify({
                success: true,
                data: {
                    coinsSpent: amount,
                    newCoins,
                    reason: reason || '購買物品'
                }
            }));

        } catch (error) {
            console.error('❌ 消費金錢失敗:', error.message);
            res.writeHead(500, { 'Content-Type': 'application/json' });
            res.end(JSON.stringify({
                success: false,
                message: '消費失敗'
            }));
        }
    });

    // 消費鑽石
    app.post('/api/user/spend-diamonds', async (req, res) => {
        try {
            const userId = verifyToken(req);
            const { amount, reason } = req.body;

            if (!amount || amount <= 0) {
                res.writeHead(400, { 'Content-Type': 'application/json' });
                res.end(JSON.stringify({
                    success: false,
                    message: '消費數量無效'
                }));
                return;
            }

            await database.connect();
            const users = database.getUsersCollection();

            const user = await users.findOne({ _id: new ObjectId(userId) });
            if (!user) {
                res.writeHead(404, { 'Content-Type': 'application/json' });
                res.end(JSON.stringify({
                    success: false,
                    message: '用戶不存在'
                }));
                return;
            }

            const currentDiamonds = user.diamonds || 50;
            if (currentDiamonds < amount) {
                res.writeHead(400, { 'Content-Type': 'application/json' });
                res.end(JSON.stringify({
                    success: false,
                    message: '鑽石不足',
                    data: {
                        required: amount,
                        current: currentDiamonds,
                        shortage: amount - currentDiamonds
                    }
                }));
                return;
            }

            const newDiamonds = currentDiamonds - amount;

            await users.updateOne(
                { _id: new ObjectId(userId) },
                {
                    $set: {
                        diamonds: newDiamonds,
                        lastActiveAt: new Date()
                    }
                }
            );

            res.writeHead(200, { 'Content-Type': 'application/json' });
            res.end(JSON.stringify({
                success: true,
                data: {
                    diamondsSpent: amount,
                    newDiamonds,
                    reason: reason || '購買物品'
                }
            }));

        } catch (error) {
            console.error('❌ 消費鑽石失敗:', error.message);
            res.writeHead(500, { 'Content-Type': 'application/json' });
            res.end(JSON.stringify({
                success: false,
                message: '消費失敗'
            }));
        }
    });
}

module.exports = {
    setupUserEconomyRoutes,
    LEVEL_CONFIG,
    DAILY_REWARDS,
    calculateLevel
};
