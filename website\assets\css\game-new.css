/* P1P2方塊大亂鬥 - 全新手遊風格 */

/* ===== CSS變量 ===== */
:root {
  /* 主色調 */
  --primary-color: #6366f1;
  --primary-light: #818cf8;
  --primary-dark: #4f46e5;

  /* 輔助色 */
  --secondary-color: #ec4899;
  --accent-color: #f59e0b;
  --success-color: #10b981;
  --warning-color: #f59e0b;
  --danger-color: #ef4444;

  /* 背景色 */
  --bg-primary: #0f0f23;
  --bg-secondary: #1a1a2e;
  --bg-tertiary: #16213e;
  --bg-card: rgba(255, 255, 255, 0.05);
  --bg-card-hover: rgba(255, 255, 255, 0.1);

  /* 文字色 */
  --text-primary: #ffffff;
  --text-secondary: #cbd5e1;
  --text-muted: #64748b;

  /* 陰影 */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
  --shadow-glow: 0 0 20px rgba(99, 102, 241, 0.3);

  /* 間距 */
  --space-1: 0.25rem;
  --space-2: 0.5rem;
  --space-3: 0.75rem;
  --space-4: 1rem;
  --space-5: 1.25rem;
  --space-6: 1.5rem;
  --space-8: 2rem;
  --space-10: 2.5rem;
  --space-12: 3rem;

  /* 字體 */
  --font-primary: 'Noto Sans TC', sans-serif;
  --font-gaming: 'Orbitron', monospace;

  /* 動畫 */
  --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  --transition-fast: all 0.15s ease;
}

/* ===== 基礎樣式 ===== */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: var(--font-primary);
  background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
  color: var(--text-primary);
  overflow-x: hidden;
  min-height: 100vh;
}

/* ===== 載入畫面 ===== */
.loading-screen {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  transition: opacity 0.5s ease;
}

.loading-content {
  text-align: center;
  color: white;
}

.game-logo {
  margin-bottom: var(--space-8);
}

.game-logo img {
  width: 80px;
  height: 80px;
  margin-bottom: var(--space-4);
  border-radius: 50%;
  box-shadow: var(--shadow-glow);
}

.game-logo h1 {
  font-family: var(--font-gaming);
  font-size: 2.5rem;
  font-weight: 900;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
  margin-bottom: var(--space-2);
}

.loading-spinner {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--space-4);
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(255, 255, 255, 0.3);
  border-top: 4px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

#loading-text {
  font-size: 1.1rem;
  opacity: 0.9;
}

/* ===== 遊戲界面 ===== */
.game-interface {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* ===== 頂部狀態欄 ===== */
.game-header {
  background: rgba(26, 26, 46, 0.95);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  padding: var(--space-4) var(--space-6);
  display: grid;
  grid-template-columns: 1fr auto 1fr;
  align-items: center;
  gap: var(--space-4);
  position: sticky;
  top: 0;
  z-index: 100;
  box-shadow: var(--shadow-lg);
}

/* 左側品牌 */
.header-brand {
  display: flex;
  align-items: center;
  gap: var(--space-3);
}

.brand-logo {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  box-shadow: var(--shadow-md);
}

.brand-info {
  display: flex;
  flex-direction: column;
}

.game-title {
  font-family: var(--font-gaming);
  font-size: 1.5rem;
  font-weight: 900;
  color: var(--primary-light);
  line-height: 1;
}

.game-subtitle {
  font-size: 0.875rem;
  color: var(--text-secondary);
  margin-top: 2px;
}

/* 中央資源 */
.header-resources {
  display: flex;
  gap: var(--space-4);
  justify-content: center;
}

.resource-item {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  background: var(--bg-card);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: var(--space-2) var(--space-4);
  transition: var(--transition);
}

.resource-item:hover {
  background: var(--bg-card-hover);
  transform: translateY(-2px);
}

.resource-item i {
  font-size: 1.2rem;
}

.resource-item.coins i {
  color: var(--accent-color);
}

.resource-item.gems i {
  color: var(--secondary-color);
}

.resource-value {
  font-weight: 600;
  font-size: 1rem;
  min-width: 40px;
  text-align: center;
}

.resource-add {
  width: 24px;
  height: 24px;
  border: none;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  color: white;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.75rem;
  transition: var(--transition-fast);
}

.resource-add:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.1);
}

/* 右側用戶 */
.header-user {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  justify-content: flex-end;
}

.user-avatar {
  position: relative;
}

.user-avatar img {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  border: 2px solid var(--primary-light);
  box-shadow: var(--shadow-md);
}

.level-badge {
  position: absolute;
  bottom: -4px;
  right: -4px;
  width: 24px;
  height: 24px;
  background: linear-gradient(135deg, var(--accent-color) 0%, #f97316 100%);
  border: 2px solid var(--bg-secondary);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.75rem;
  font-weight: 700;
  color: white;
}

.user-info {
  display: flex;
  flex-direction: column;
  gap: var(--space-1);
}

.username {
  font-weight: 600;
  font-size: 1rem;
  color: var(--text-primary);
}

.user-exp {
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

.exp-bar {
  width: 80px;
  height: 6px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 3px;
  overflow: hidden;
}

.exp-fill {
  height: 100%;
  background: linear-gradient(90deg, var(--success-color) 0%, #22c55e 100%);
  border-radius: 3px;
  transition: width 0.3s ease;
}

.exp-text {
  font-size: 0.75rem;
  color: var(--text-secondary);
  font-weight: 500;
}

.header-actions {
  display: flex;
  gap: var(--space-2);
}

.action-btn {
  width: 40px;
  height: 40px;
  border: none;
  border-radius: 10px;
  background: var(--bg-card);
  border: 1px solid rgba(255, 255, 255, 0.1);
  color: var(--text-secondary);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1rem;
  transition: var(--transition);
}

.action-btn:hover {
  background: var(--bg-card-hover);
  color: var(--text-primary);
  transform: translateY(-2px);
}

/* ===== 響應式設計 - 頭部 ===== */
@media (max-width: 768px) {
  .game-header {
    grid-template-columns: 1fr;
    grid-template-rows: auto auto auto;
    gap: var(--space-3);
    text-align: center;
  }

  .header-brand {
    justify-content: center;
  }

  .header-resources {
    order: 2;
  }

  .header-user {
    order: 3;
    justify-content: center;
  }

  .game-title {
    font-size: 1.25rem;
  }

  .resource-item {
    padding: var(--space-2) var(--space-3);
  }
}

@media (max-width: 480px) {
  .game-header {
    padding: var(--space-3) var(--space-4);
  }

  .header-resources {
    gap: var(--space-2);
  }

  .resource-item {
    padding: var(--space-1) var(--space-2);
  }

  .resource-value {
    font-size: 0.9rem;
    min-width: 30px;
  }

  .user-avatar img {
    width: 40px;
    height: 40px;
  }

  .level-badge {
    width: 20px;
    height: 20px;
    font-size: 0.7rem;
  }
}

/* ===== 主要內容區域 ===== */
.game-main {
  flex: 1;
  position: relative;
  overflow-y: auto;
}

/* 背景裝飾 */
.background-decoration {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  overflow: hidden;
}

.bg-circle {
  position: absolute;
  border-radius: 50%;
  opacity: 0.1;
  animation: float 20s ease-in-out infinite;
}

.bg-circle-1 {
  width: 300px;
  height: 300px;
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
  top: 10%;
  left: -10%;
  animation-delay: 0s;
}

.bg-circle-2 {
  width: 200px;
  height: 200px;
  background: linear-gradient(135deg, var(--secondary-color) 0%, var(--accent-color) 100%);
  top: 60%;
  right: -5%;
  animation-delay: -7s;
}

.bg-circle-3 {
  width: 150px;
  height: 150px;
  background: linear-gradient(135deg, var(--accent-color) 0%, var(--success-color) 100%);
  bottom: 20%;
  left: 20%;
  animation-delay: -14s;
}

@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  33% { transform: translateY(-30px) rotate(120deg); }
  66% { transform: translateY(-15px) rotate(240deg); }
}

/* 遊戲內容 */
.game-content {
  position: relative;
  z-index: 10;
  padding: var(--space-8) var(--space-6);
  max-width: 1200px;
  margin: 0 auto;
}

/* ===== 主要遊戲模式 ===== */
.main-modes {
  margin-bottom: var(--space-10);
}

.mode-card.featured {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
  border-radius: 20px;
  padding: var(--space-8);
  position: relative;
  overflow: hidden;
  cursor: pointer;
  transition: var(--transition);
  box-shadow: var(--shadow-xl);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.mode-card.featured:hover {
  transform: translateY(-8px);
  box-shadow: 0 25px 50px rgba(99, 102, 241, 0.4);
}

.mode-card.featured .card-bg {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 30% 20%, rgba(255, 255, 255, 0.2) 0%, transparent 50%);
  pointer-events: none;
}

.mode-card.featured .card-content {
  position: relative;
  display: flex;
  align-items: center;
  gap: var(--space-6);
}

.mode-card.featured .mode-icon {
  width: 80px;
  height: 80px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2.5rem;
  color: white;
  box-shadow: var(--shadow-lg);
}

.mode-card.featured .mode-info {
  flex: 1;
}

.mode-card.featured .mode-info h2 {
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: var(--space-2);
  color: white;
}

.mode-card.featured .mode-info p {
  font-size: 1.1rem;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: var(--space-4);
}

.mode-stats {
  display: flex;
  gap: var(--space-6);
}

.mode-stats span {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  color: rgba(255, 255, 255, 0.8);
  font-size: 0.9rem;
}

.play-btn {
  width: 80px;
  height: 80px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2rem;
  color: white;
  transition: var(--transition);
  box-shadow: var(--shadow-lg);
}

.mode-card.featured:hover .play-btn {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.1);
}

.hot-badge {
  position: absolute;
  top: 20px;
  right: 20px;
  background: linear-gradient(135deg, var(--accent-color) 0%, #f97316 100%);
  color: white;
  padding: var(--space-2) var(--space-4);
  border-radius: 20px;
  font-size: 0.875rem;
  font-weight: 600;
  box-shadow: var(--shadow-md);
  animation: pulse 2s ease-in-out infinite;
}

@keyframes pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.05); }
}

/* ===== 其他遊戲模式 ===== */
.other-modes {
  margin-bottom: var(--space-10);
}

.mode-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: var(--space-6);
}

.mode-grid .mode-card {
  background: var(--bg-card);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  padding: var(--space-6);
  position: relative;
  overflow: hidden;
  cursor: pointer;
  transition: var(--transition);
  backdrop-filter: blur(20px);
}

.mode-grid .mode-card:hover {
  background: var(--bg-card-hover);
  transform: translateY(-4px);
  box-shadow: var(--shadow-lg);
}

.mode-grid .mode-card .card-bg {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  opacity: 0;
  transition: var(--transition);
}

.mode-grid .mode-card:nth-child(1) .card-bg {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
}

.mode-grid .mode-card:nth-child(2) .card-bg {
  background: linear-gradient(135deg, var(--success-color) 0%, #22c55e 100%);
}

.mode-grid .mode-card:nth-child(3) .card-bg {
  background: linear-gradient(135deg, var(--secondary-color) 0%, #f472b6 100%);
}

.mode-grid .mode-card:nth-child(4) .card-bg {
  background: linear-gradient(135deg, var(--accent-color) 0%, #f97316 100%);
}

.mode-grid .mode-card:hover .card-bg {
  opacity: 0.1;
}

.mode-grid .mode-card .card-content {
  position: relative;
  display: flex;
  align-items: center;
  gap: var(--space-4);
}

.mode-grid .mode-card .mode-icon {
  width: 60px;
  height: 60px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  color: var(--text-primary);
}

.mode-grid .mode-card .mode-info h3 {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: var(--space-1);
  color: var(--text-primary);
}

.mode-grid .mode-card .mode-info p {
  font-size: 0.9rem;
  color: var(--text-secondary);
}

/* ===== 底部信息區域 ===== */
.bottom-info {
  margin-top: var(--space-8);
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--space-6);
}

.info-card {
  background: var(--bg-card);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  padding: var(--space-6);
  backdrop-filter: blur(20px);
}

.info-card h3 {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: var(--space-4);
  color: var(--text-primary);
}

.info-card h3 i {
  color: var(--primary-light);
}

/* 每日任務 */
.task-list {
  display: flex;
  flex-direction: column;
  gap: var(--space-3);
}

.task-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-3);
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
}

.task-desc {
  color: var(--text-secondary);
}

.task-reward {
  display: flex;
  align-items: center;
  gap: var(--space-1);
  color: var(--accent-color);
  font-weight: 600;
}

/* 排行榜 */
.leaderboard {
  display: flex;
  flex-direction: column;
  gap: var(--space-3);
  margin-bottom: var(--space-4);
}

.rank-item {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  padding: var(--space-3);
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
}

.rank {
  width: 24px;
  height: 24px;
  background: var(--primary-color);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.75rem;
  font-weight: 600;
}

.player {
  flex: 1;
  color: var(--text-secondary);
}

.score {
  color: var(--accent-color);
  font-weight: 600;
}

.view-more {
  background: none;
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: var(--text-secondary);
  padding: var(--space-2) var(--space-4);
  border-radius: 8px;
  cursor: pointer;
  transition: var(--transition-fast);
}

.view-more:hover {
  background: rgba(255, 255, 255, 0.1);
  color: var(--text-primary);
}

/* 最近活動 */
.activity-list {
  display: flex;
  flex-direction: column;
  gap: var(--space-3);
}

.activity-item {
  display: flex;
  flex-direction: column;
  gap: var(--space-1);
  padding: var(--space-3);
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
}

.activity-desc {
  color: var(--text-secondary);
}

.activity-time {
  color: var(--text-muted);
  font-size: 0.75rem;
}

/* ===== 模態框 ===== */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(4px);
}

.modal {
  background: var(--bg-secondary);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 16px;
  width: 90%;
  max-width: 500px;
  max-height: 80vh;
  overflow: hidden;
  box-shadow: var(--shadow-xl);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-6);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.modal-header h3 {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-primary);
}

.modal-close {
  width: 32px;
  height: 32px;
  border: none;
  background: none;
  color: var(--text-secondary);
  cursor: pointer;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: var(--transition-fast);
}

.modal-close:hover {
  background: rgba(255, 255, 255, 0.1);
  color: var(--text-primary);
}

.modal-body {
  padding: var(--space-6);
  color: var(--text-secondary);
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: var(--space-3);
  padding: var(--space-6);
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.btn {
  padding: var(--space-3) var(--space-6);
  border: none;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: var(--transition-fast);
}

.btn-primary {
  background: var(--primary-color);
  color: white;
}

.btn-primary:hover {
  background: var(--primary-dark);
}

.btn-secondary {
  background: transparent;
  color: var(--text-secondary);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.btn-secondary:hover {
  background: rgba(255, 255, 255, 0.1);
  color: var(--text-primary);
}

/* ===== 通知系統 ===== */
.notification-container {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 1100;
  display: flex;
  flex-direction: column;
  gap: var(--space-3);
}

.notification {
  background: var(--bg-secondary);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  padding: var(--space-4);
  min-width: 300px;
  box-shadow: var(--shadow-lg);
  backdrop-filter: blur(20px);
  animation: slideIn 0.3s ease;
}

.notification.success {
  border-left: 4px solid var(--success-color);
}

.notification.warning {
  border-left: 4px solid var(--warning-color);
}

.notification.error {
  border-left: 4px solid var(--danger-color);
}

.notification.info {
  border-left: 4px solid var(--primary-color);
}

@keyframes slideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* ===== 響應式設計 ===== */
@media (max-width: 1024px) {
  .game-content {
    padding: var(--space-6) var(--space-4);
  }

  .mode-card.featured {
    padding: var(--space-6);
  }

  .mode-card.featured .card-content {
    flex-direction: column;
    text-align: center;
    gap: var(--space-4);
  }

  .mode-stats {
    justify-content: center;
  }
}

@media (max-width: 768px) {
  .game-header {
    padding: var(--space-3) var(--space-4);
    flex-wrap: wrap;
    gap: var(--space-3);
  }

  .header-resources {
    order: 3;
    width: 100%;
    justify-content: center;
  }

  .resource-item {
    flex: 1;
    justify-content: center;
  }

  .game-content {
    padding: var(--space-4) var(--space-3);
  }

  .mode-card.featured .mode-info h2 {
    font-size: 1.5rem;
  }

  .mode-grid {
    grid-template-columns: 1fr;
  }

  .info-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 480px) {
  .brand-info {
    display: none;
  }

  .user-info {
    display: none;
  }

  .header-actions {
    gap: var(--space-1);
  }

  .action-btn {
    width: 36px;
    height: 36px;
  }

  .mode-card.featured {
    padding: var(--space-4);
  }

  .mode-card.featured .mode-icon,
  .play-btn {
    width: 60px;
    height: 60px;
    font-size: 1.5rem;
  }

  .modal {
    width: 95%;
    margin: var(--space-4);
  }
}

/* ===== 通知系統 ===== */
.notification-container {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 10000;
  display: flex;
  flex-direction: column;
  gap: var(--space-3);
  max-width: 400px;
}

.notification {
  background: var(--bg-card);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: var(--space-4);
  box-shadow: var(--shadow-lg);
  backdrop-filter: blur(20px);
  transform: translateX(100%);
  animation: slideInNotification 0.3s ease forwards;
}

.notification-success {
  border-left: 4px solid var(--success-color);
}

.notification-warning {
  border-left: 4px solid var(--warning-color);
}

.notification-error {
  border-left: 4px solid var(--danger-color);
}

.notification-info {
  border-left: 4px solid var(--primary-color);
}

.notification-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: var(--space-3);
}

.notification-message {
  color: var(--text-primary);
  font-size: 0.9rem;
  line-height: 1.4;
}

.notification-close {
  background: none;
  border: none;
  color: var(--text-secondary);
  cursor: pointer;
  font-size: 1.2rem;
  padding: 0;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: var(--transition-fast);
}

.notification-close:hover {
  background: rgba(255, 255, 255, 0.1);
  color: var(--text-primary);
}

@keyframes slideInNotification {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}
