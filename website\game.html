<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>P1P2方塊大亂鬥 - 遊戲大廳</title>

    <!-- 字體和圖標 -->
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+TC:wght@300;400;500;700;900&family=Orbitron:wght@400;700;900&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- 緩存破壞系統 -->
    <script src="/assets/js/cache-buster.js?v=20250529-fix"></script>

    <!-- 樣式 -->
    <link rel="stylesheet" href="/assets/css/game-new.css?v=20250529-fix">
    <link rel="stylesheet" href="/assets/css/game-match.css?v=20250529-fix">
    <link rel="stylesheet" href="/assets/css/block-selection.css?v=20250529-fix">
</head>
<body>
    <!-- 載入畫面 -->
    <div id="loading-screen" class="loading-screen">
        <div class="loading-content">
            <div class="game-logo">
                <div class="logo-text">P1P2</div>
                <h1>P1P2方塊大亂鬥</h1>
            </div>
            <div class="loading-spinner">
                <div class="spinner"></div>
                <p id="loading-text">正在載入遊戲...</p>
            </div>
        </div>
    </div>

    <!-- 主遊戲界面 -->
    <div id="game-interface" class="game-interface" style="display: none;">
        <!-- 頂部狀態欄 -->
        <header class="game-header">
            <!-- 左側品牌 -->
            <div class="header-brand">
                <img src="/assets/images/logo.svg?v=20250529-fix" alt="P1P2" class="brand-logo">
                <div class="brand-info">
                    <h1 class="game-title">P1P2</h1>
                    <span class="game-subtitle">方塊大亂鬥</span>
                </div>
            </div>

            <!-- 中央資源 -->
            <div class="header-resources">
                <div class="resource-item coins">
                    <i class="fas fa-coins"></i>
                    <span class="resource-value">1,000</span>
                    <button class="resource-add"><i class="fas fa-plus"></i></button>
                </div>
                <div class="resource-item gems">
                    <i class="fas fa-gem"></i>
                    <span class="resource-value">50</span>
                    <button class="resource-add"><i class="fas fa-plus"></i></button>
                </div>
                <div class="resource-item energy">
                    <i class="fas fa-bolt"></i>
                    <span class="resource-value">100</span>
                    <button class="resource-add"><i class="fas fa-plus"></i></button>
                </div>
            </div>

            <!-- 右側用戶 -->
            <div class="header-user">
                <div class="user-avatar">
                    <img id="user-avatar" src="/assets/images/default-avatar.png?v=20250529-fix" alt="用戶頭像">
                    <div class="level-badge">
                        <span id="user-level">1</span>
                    </div>
                </div>
                <div class="user-info">
                    <div class="username" id="user-name">玩家</div>
                    <div class="user-exp">
                        <div class="exp-bar">
                            <div class="exp-fill" style="width: 65%"></div>
                        </div>
                        <span class="exp-text">65/100</span>
                    </div>
                </div>
                <div class="header-actions">
                    <button class="action-btn" onclick="showSettings()">
                        <i class="fas fa-cog"></i>
                    </button>
                    <button class="action-btn" onclick="logout()">
                        <i class="fas fa-sign-out-alt"></i>
                    </button>
                </div>
            </div>
        </header>

        <!-- 主要內容區域 -->
        <main class="game-main">
            <!-- 背景裝飾 -->
            <div class="background-decoration">
                <div class="bg-circle bg-circle-1"></div>
                <div class="bg-circle bg-circle-2"></div>
                <div class="bg-circle bg-circle-3"></div>
            </div>

            <!-- 遊戲內容 -->
            <div class="game-content">
                <!-- 主要遊戲模式 -->
                <section class="main-modes">
                    <div class="mode-card featured" onclick="gameMatch.startQuickMatch()">
                        <div class="card-bg"></div>
                        <div class="card-content">
                            <div class="mode-icon">
                                <i class="fas fa-bolt"></i>
                            </div>
                            <div class="mode-info">
                                <h2>快速遊戲</h2>
                                <p>立即匹配對手開始戰鬥</p>
                                <div class="mode-stats">
                                    <span><i class="fas fa-users"></i> <span id="online-players">1,234</span> 在線</span>
                                    <span><i class="fas fa-clock"></i> 平均 <span id="avg-match-time">2</span>分鐘</span>
                                </div>
                            </div>
                            <div class="play-btn">
                                <i class="fas fa-play"></i>
                            </div>
                        </div>
                        <div class="hot-badge">熱門</div>
                    </div>
                </section>

                <!-- 其他遊戲模式 -->
                <section class="other-modes">
                    <div class="mode-grid">


                        <div class="mode-card" onclick="singlePlayer()">
                            <div class="card-bg"></div>
                            <div class="card-content">
                                <div class="mode-icon">
                                    <i class="fas fa-robot"></i>
                                </div>
                                <div class="mode-info">
                                    <h3>單人模式</h3>
                                    <p>挑戰AI對手</p>
                                </div>
                            </div>
                        </div>

                        <div class="mode-card" onclick="tutorial()">
                            <div class="card-bg"></div>
                            <div class="card-content">
                                <div class="mode-icon">
                                    <i class="fas fa-graduation-cap"></i>
                                </div>
                                <div class="mode-info">
                                    <h3>新手教學</h3>
                                    <p>學習遊戲基礎</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- 底部信息區域 -->
                <section class="bottom-info">
                    <div class="info-grid">
                        <!-- 每日任務 -->
                        <div class="info-card">
                            <h3><i class="fas fa-tasks"></i> 每日任務</h3>
                            <div class="task-list">
                                <div class="task-item">
                                    <span class="task-desc">登入遊戲</span>
                                    <span class="task-reward">+10 <i class="fas fa-coins"></i></span>
                                </div>
                                <div class="task-item">
                                    <span class="task-desc">進行3場對戰</span>
                                    <span class="task-reward">+50 <i class="fas fa-coins"></i></span>
                                </div>
                            </div>
                        </div>

                        <!-- 排行榜 -->
                        <div class="info-card">
                            <h3><i class="fas fa-trophy"></i> 排行榜</h3>
                            <div class="leaderboard">
                                <div class="rank-item">
                                    <span class="rank">1</span>
                                    <span class="player">傳說玩家</span>
                                    <span class="score">2850</span>
                                </div>
                                <div class="rank-item">
                                    <span class="rank">2</span>
                                    <span class="player">高手玩家</span>
                                    <span class="score">2720</span>
                                </div>
                            </div>
                            <button class="view-more">查看更多</button>
                        </div>

                        <!-- 最近活動 -->
                        <div class="info-card">
                            <h3><i class="fas fa-bell"></i> 最近活動</h3>
                            <div class="activity-list">
                                <div class="activity-item">
                                    <span class="activity-desc">獲得成就：「初出茅廬」</span>
                                    <span class="activity-time">2分鐘前</span>
                                </div>
                                <div class="activity-item">
                                    <span class="activity-desc">等級提升到 Lv.2</span>
                                    <span class="activity-time">1小時前</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>
            </div>
        </main>
    </div>

    <!-- 模態框 -->
    <div id="modal-overlay" class="modal-overlay" style="display: none;">
        <div class="modal">
            <div class="modal-header">
                <h3 id="modal-title">標題</h3>
                <button class="modal-close" onclick="closeModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <p id="modal-content">內容</p>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="closeModal()">取消</button>
                <button class="btn btn-primary" onclick="closeModal()">確定</button>
            </div>
        </div>
    </div>

    <!-- 通知系統 -->
    <div id="notification-container" class="notification-container"></div>

    <!-- JavaScript -->
    <!-- 系統穩定性模組 -->
    <script src="/assets/js/system-monitor.js?v=20250529-fix"></script>
    <script src="/assets/js/connection-manager.js?v=20250529-fix"></script>
    <script src="/assets/js/game-state-manager.js?v=20250529-fix"></script>

    <!-- 並發處理模組 -->
    <script src="/assets/js/load-balancer.js?v=20250529-fix"></script>
    <script src="/assets/js/concurrency-manager.js?v=20250529-fix"></script>
    <script src="/assets/js/room-manager.js?v=20250529-fix"></script>

    <script src="/assets/js/game.js?v=20250529-fix"></script>
    <script src="/assets/js/game-match.js?v=20250529-fix"></script>
    <script src="/assets/js/block-selection.js?v=20250529-fix"></script>

    <!-- 初始化腳本 -->
    <script>
        // 等待DOM載入完成
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🎮 P1P2方塊大亂鬥 - 遊戲頁面載入中...');

            // 初始化DOM元素
            loadingScreen = document.getElementById('loading-screen');
            gameInterface = document.getElementById('game-interface');
            modalOverlay = document.getElementById('modal-overlay');
            notificationContainer = document.getElementById('notification-container');

            // 檢查必要元素是否存在
            if (!loadingScreen || !gameInterface) {
                console.error('❌ 關鍵DOM元素缺失');
                return;
            }

            // 初始化遊戲
            try {
                initializeGame();
            } catch (error) {
                console.error('❌ 遊戲初始化失敗:', error);
                // 顯示錯誤界面
                if (loadingScreen) {
                    loadingScreen.innerHTML = `
                        <div class="loading-content">
                            <div class="game-logo">
                                <h1>P1P2方塊大亂鬥</h1>
                            </div>
                            <div style="color: #ff6b6b; text-align: center;">
                                <p>遊戲載入失敗</p>
                                <p style="font-size: 0.9rem; opacity: 0.8;">請刷新頁面重試</p>
                                <button onclick="location.reload()" style="
                                    background: #ff6b6b;
                                    color: white;
                                    border: none;
                                    padding: 10px 20px;
                                    border-radius: 5px;
                                    cursor: pointer;
                                    margin-top: 20px;
                                ">重新載入</button>
                            </div>
                        </div>
                    `;
                }
            }
        });

        // 全局錯誤處理
        window.addEventListener('error', function(event) {
            console.error('🚨 頁面錯誤:', event.error);
        });

        // 全局未處理的Promise拒絕
        window.addEventListener('unhandledrejection', function(event) {
            console.error('🚨 未處理的Promise拒絕:', event.reason);
        });

        // 通用模態框和通知函數
        function showModal(title, content, buttons = []) {
            if (!modalOverlay) return;

            const modal = modalOverlay.querySelector('.modal');
            if (!modal) return;

            modal.querySelector('#modal-title').textContent = title;
            modal.querySelector('#modal-content').innerHTML = content;

            modalOverlay.style.display = 'flex';
        }

        function closeModal() {
            if (modalOverlay) {
                modalOverlay.style.display = 'none';
            }
        }

        function showNotification(message, type = 'info') {
            if (!notificationContainer) {
                console.log(`📢 通知 [${type}]: ${message}`);
                return;
            }

            const notification = document.createElement('div');
            notification.className = `notification notification-${type}`;
            notification.innerHTML = `
                <div class="notification-content">
                    <span class="notification-message">${message}</span>
                    <button class="notification-close" onclick="this.parentElement.parentElement.remove()">×</button>
                </div>
            `;

            notificationContainer.appendChild(notification);

            // 自動移除通知
            setTimeout(() => {
                if (notification.parentElement) {
                    notification.remove();
                }
            }, 5000);
        }

        // 設置和登出函數
        function showSettings() {
            showModal('設置', `
                <div style="text-align: center;">
                    <p>設置功能開發中，敬請期待！</p>
                </div>
            `);
        }

        function logout() {
            if (confirm('確定要登出嗎？')) {
                localStorage.removeItem('user');
                localStorage.removeItem('token');
                window.location.href = '/auth';
            }
        }
    </script>
</body>
</html>
