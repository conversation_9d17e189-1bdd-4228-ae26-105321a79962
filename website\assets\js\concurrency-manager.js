/**
 * P1P2方塊大亂鬥 - 並發管理器
 * 負責處理多組玩家同時進入遊戲的情況
 */

class ConcurrencyManager {
    constructor() {
        this.maxConcurrentGames = 100; // 最大同時進行的遊戲數量
        this.maxPlayersPerServer = 1000; // 每個服務器最大玩家數
        this.activeGames = new Map(); // 活躍遊戲列表
        this.playerSessions = new Map(); // 玩家會話管理
        this.serverLoad = {
            cpu: 0,
            memory: 0,
            connections: 0,
            games: 0
        };

        this.queueManagement = {
            matchQueue: [],
            battleQueue: [],
            maxQueueSize: 500
        };

        this.loadBalancing = {
            servers: [],
            currentServer: 0,
            healthCheck: true
        };

        this.init();
    }

    init() {
        console.log('🚀 並發管理器初始化...');

        // 設置負載監控
        this.setupLoadMonitoring();

        // 設置隊列管理
        this.setupQueueManagement();

        // 設置負載均衡
        this.setupLoadBalancing();

        // 設置資源清理
        this.setupResourceCleanup();

        console.log('✅ 並發管理器已啟動');
    }

    // 負載監控
    setupLoadMonitoring() {
        setInterval(() => {
            this.updateServerLoad();
            this.checkServerCapacity();
        }, 5000); // 每5秒檢查一次
    }

    // 更新服務器負載
    updateServerLoad() {
        this.serverLoad = {
            cpu: this.getCPUUsage(),
            memory: this.getMemoryUsage(),
            connections: this.getActiveConnections(),
            games: this.activeGames.size
        };

        // 記錄負載狀態
        if (this.serverLoad.games > 50) {
            console.log(`📊 服務器負載: ${this.serverLoad.games} 個活躍遊戲`);
        }
    }

    // 檢查服務器容量
    checkServerCapacity() {
        const loadPercentage = this.calculateLoadPercentage();

        if (loadPercentage > 80) {
            console.warn('⚠️ 服務器負載過高:', loadPercentage + '%');
            this.enableLoadReduction();
        } else if (loadPercentage > 90) {
            console.error('🚨 服務器負載危險:', loadPercentage + '%');
            this.enableEmergencyMode();
        }
    }

    // 計算負載百分比
    calculateLoadPercentage() {
        const gameLoad = (this.serverLoad.games / this.maxConcurrentGames) * 100;
        const connectionLoad = (this.serverLoad.connections / this.maxPlayersPerServer) * 100;
        const memoryLoad = this.serverLoad.memory;

        return Math.max(gameLoad, connectionLoad, memoryLoad);
    }

    // 隊列管理
    setupQueueManagement() {
        // 定期處理隊列
        setInterval(() => {
            this.processMatchQueue();
            this.processBattleQueue();
            this.cleanupExpiredQueues();
        }, 1000); // 每秒處理一次
    }

    // 處理匹配隊列
    processMatchQueue() {
        const { matchQueue } = this.queueManagement;

        // 批量處理匹配請求
        const batchSize = this.calculateOptimalBatchSize();
        const batch = matchQueue.splice(0, batchSize);

        if (batch.length >= 2) {
            this.createMultipleMatches(batch);
        }
    }

    // 處理戰鬥隊列
    processBattleQueue() {
        const { battleQueue } = this.queueManagement;

        // 處理等待中的戰鬥
        const now = Date.now();
        const battleTimeout = 30000; // 30秒超時

        battleQueue.forEach((battle, index) => {
            if (now - battle.createdAt > battleTimeout) {
                // 移除超時的戰鬥請求
                battleQueue.splice(index, 1);
                console.log(`⏰ 戰鬥請求超時: ${battle.battleId}`);

                // 通知玩家戰鬥超時
                this.notifyBattleTimeout(battle);
            } else if (battle.status === 'waiting_for_players') {
                // 檢查玩家是否都已準備
                this.checkBattleReadiness(battle);
            }
        });
    }

    // 計算最佳批處理大小
    calculateOptimalBatchSize() {
        const loadPercentage = this.calculateLoadPercentage();

        if (loadPercentage < 50) {
            return 20; // 低負載時批量處理更多
        } else if (loadPercentage < 80) {
            return 10; // 中等負載
        } else {
            return 4; // 高負載時減少批量
        }
    }

    // 創建多個匹配
    createMultipleMatches(players) {
        const matches = [];

        // 將玩家配對
        for (let i = 0; i < players.length - 1; i += 2) {
            const player1 = players[i];
            const player2 = players[i + 1];

            if (player1 && player2) {
                const match = this.createMatch(player1, player2);
                matches.push(match);
            }
        }

        // 並行處理所有匹配
        Promise.all(matches.map(match => this.processMatch(match)))
            .then(results => {
                console.log(`✅ 成功創建 ${results.length} 個匹配`);
            })
            .catch(error => {
                console.error('❌ 批量匹配處理失敗:', error);
            });
    }

    // 創建單個匹配
    createMatch(player1, player2) {
        const matchId = this.generateMatchId();

        const match = {
            matchId,
            players: [player1, player2],
            status: 'created',
            createdAt: Date.now(),
            gameMode: 'classic_duel'
        };

        this.activeGames.set(matchId, match);
        return match;
    }

    // 處理匹配
    async processMatch(match) {
        try {
            // 通知玩家匹配成功
            await this.notifyMatchFound(match);

            // 等待玩家確認
            await this.waitForPlayerConfirmation(match);

            // 開始遊戲
            await this.startGame(match);

            return match;
        } catch (error) {
            console.error(`❌ 匹配處理失敗 [${match.matchId}]:`, error);
            this.cleanupMatch(match.matchId);
            throw error;
        }
    }

    // 通知匹配成功
    async notifyMatchFound(match) {
        const notifications = match.players.map(player => {
            return this.sendPlayerNotification(player, {
                type: 'match_found',
                data: {
                    matchId: match.matchId,
                    opponent: this.getOpponentInfo(match, player),
                    gameMode: match.gameMode
                }
            });
        });

        await Promise.all(notifications);
    }

    // 等待玩家確認
    async waitForPlayerConfirmation(match, timeout = 10000) {
        return new Promise((resolve, reject) => {
            const confirmations = new Set();

            const checkConfirmations = () => {
                if (confirmations.size === match.players.length) {
                    resolve(true);
                }
            };

            // 設置確認監聽器
            match.players.forEach(player => {
                this.onPlayerConfirmation(player.id, () => {
                    confirmations.add(player.id);
                    checkConfirmations();
                });
            });

            // 設置超時
            setTimeout(() => {
                if (confirmations.size < match.players.length) {
                    reject(new Error('玩家確認超時'));
                }
            }, timeout);
        });
    }

    // 開始遊戲
    async startGame(match) {
        match.status = 'in_progress';
        match.startedAt = Date.now();

        // 創建遊戲實例
        const gameInstance = await this.createGameInstance(match);

        // 通知玩家遊戲開始
        const startNotifications = match.players.map(player => {
            return this.sendPlayerNotification(player, {
                type: 'game_start',
                data: {
                    matchId: match.matchId,
                    gameInstance: gameInstance.id,
                    battleUrl: `/battle?match=${match.matchId}`
                }
            });
        });

        await Promise.all(startNotifications);

        console.log(`🎮 遊戲開始 [${match.matchId}]`);
    }

    // 創建遊戲實例
    async createGameInstance(match) {
        const gameInstance = {
            id: this.generateGameInstanceId(),
            matchId: match.matchId,
            players: match.players,
            status: 'active',
            createdAt: Date.now(),
            settings: {
                timeLimit: 180,
                arena: 'standard'
            }
        };

        // 分配服務器資源
        await this.allocateServerResources(gameInstance);

        return gameInstance;
    }

    // 分配服務器資源
    async allocateServerResources(gameInstance) {
        // 檢查資源可用性
        if (!this.hasAvailableResources()) {
            throw new Error('服務器資源不足');
        }

        // 分配內存和CPU
        this.reserveResources(gameInstance);

        // 創建遊戲房間
        await this.createGameRoom(gameInstance);
    }

    // 檢查可用資源
    hasAvailableResources() {
        return this.serverLoad.games < this.maxConcurrentGames &&
               this.serverLoad.connections < this.maxPlayersPerServer &&
               this.serverLoad.memory < 80;
    }

    // 預留資源
    reserveResources(gameInstance) {
        this.serverLoad.games++;
        this.serverLoad.connections += gameInstance.players.length;

        // 記錄資源使用
        console.log(`📊 資源分配: 遊戲 ${this.serverLoad.games}/${this.maxConcurrentGames}`);
    }

    // 負載均衡
    setupLoadBalancing() {
        // 如果有多個服務器實例，設置負載均衡
        this.loadBalancing.servers = [
            { id: 'server-1', url: 'ws://localhost:8080', load: 0 },
            // 可以添加更多服務器
        ];
    }

    // 選擇最佳服務器
    selectBestServer() {
        if (this.loadBalancing.servers.length === 0) {
            return null;
        }

        // 選擇負載最低的服務器
        return this.loadBalancing.servers.reduce((best, current) => {
            return current.load < best.load ? current : best;
        });
    }

    // 啟用負載減少模式
    enableLoadReduction() {
        console.log('🔧 啟用負載減少模式');

        // 減少匹配頻率
        this.queueManagement.maxQueueSize = 200;

        // 延長匹配時間
        this.increaseMatchTimeout();

        // 清理非活躍連接
        this.cleanupInactiveConnections();
    }

    // 啟用緊急模式
    enableEmergencyMode() {
        console.log('🚨 啟用緊急模式');

        // 停止接受新的匹配請求
        this.queueManagement.maxQueueSize = 50;

        // 強制清理資源
        this.forceResourceCleanup();

        // 通知管理員
        this.notifyAdministrators('服務器負載過高，已啟用緊急模式');
    }

    // 資源清理
    setupResourceCleanup() {
        setInterval(() => {
            this.cleanupCompletedGames();
            this.cleanupExpiredSessions();
            this.cleanupMemoryLeaks();
        }, 30000); // 每30秒清理一次
    }

    // 清理已完成的遊戲
    cleanupCompletedGames() {
        const now = Date.now();
        const gameTimeout = 10 * 60 * 1000; // 10分鐘

        this.activeGames.forEach((game, gameId) => {
            if (game.status === 'completed' ||
                (now - game.createdAt > gameTimeout)) {
                this.cleanupMatch(gameId);
            }
        });
    }

    // 清理匹配
    cleanupMatch(matchId) {
        const match = this.activeGames.get(matchId);
        if (match) {
            // 釋放資源
            this.releaseResources(match);

            // 移除匹配記錄
            this.activeGames.delete(matchId);

            console.log(`🧹 清理匹配: ${matchId}`);
        }
    }

    // 通知戰鬥超時
    notifyBattleTimeout(battle) {
        if (battle.players) {
            battle.players.forEach(player => {
                this.sendPlayerNotification(player, {
                    type: 'battle_timeout',
                    data: {
                        battleId: battle.battleId,
                        message: '戰鬥請求已超時，請重新匹配'
                    }
                });
            });
        }
    }

    // 檢查戰鬥準備狀態
    checkBattleReadiness(battle) {
        if (!battle.players || battle.players.length < 2) {
            return false;
        }

        const readyPlayers = battle.players.filter(player => player.ready === true);

        if (readyPlayers.length === battle.players.length) {
            // 所有玩家都準備好了，開始戰鬥
            this.startBattle(battle);
            return true;
        }

        return false;
    }

    // 開始戰鬥
    startBattle(battle) {
        battle.status = 'in_progress';
        battle.startedAt = Date.now();

        // 通知所有玩家戰鬥開始
        battle.players.forEach(player => {
            this.sendPlayerNotification(player, {
                type: 'battle_start',
                data: {
                    battleId: battle.battleId,
                    battleUrl: `/battle?id=${battle.battleId}`,
                    opponents: battle.players.filter(p => p.id !== player.id)
                }
            });
        });

        console.log(`⚔️ 戰鬥開始: ${battle.battleId}`);
    }

    // 釋放資源
    releaseResources(match) {
        this.serverLoad.games = Math.max(0, this.serverLoad.games - 1);
        this.serverLoad.connections = Math.max(0,
            this.serverLoad.connections - match.players.length);
    }

    // 獲取系統狀態
    getSystemStatus() {
        return {
            serverLoad: this.serverLoad,
            activeGames: this.activeGames.size,
            queueSizes: {
                match: this.queueManagement.matchQueue.length,
                battle: this.queueManagement.battleQueue.length
            },
            capacity: {
                games: `${this.serverLoad.games}/${this.maxConcurrentGames}`,
                players: `${this.serverLoad.connections}/${this.maxPlayersPerServer}`
            },
            loadPercentage: this.calculateLoadPercentage()
        };
    }

    // 工具方法
    generateMatchId() {
        return 'match_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }

    generateGameInstanceId() {
        return 'game_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }

    getCPUUsage() {
        // 簡化的CPU使用率計算
        return Math.min(100, (this.activeGames.size / this.maxConcurrentGames) * 100);
    }

    getMemoryUsage() {
        // 簡化的內存使用率計算
        if (window.performance && window.performance.memory) {
            const used = window.performance.memory.usedJSHeapSize;
            const limit = window.performance.memory.jsHeapSizeLimit;
            return (used / limit) * 100;
        }
        return (this.activeGames.size / this.maxConcurrentGames) * 50;
    }

    getActiveConnections() {
        return this.playerSessions.size;
    }

    // 發送玩家通知
    sendPlayerNotification(player, notification) {
        try {
            // 如果有WebSocket連接，通過WebSocket發送
            if (window.gameWebSocket && window.gameWebSocket.readyState === WebSocket.OPEN) {
                window.gameWebSocket.send(JSON.stringify({
                    type: 'player_notification',
                    playerId: player.id,
                    notification: notification
                }));
            } else {
                // 否則使用事件系統
                window.dispatchEvent(new CustomEvent('playerNotification', {
                    detail: { player, notification }
                }));
            }

            console.log(`📢 通知玩家 ${player.id}:`, notification.type);
            return Promise.resolve();
        } catch (error) {
            console.error('❌ 發送玩家通知失敗:', error);
            return Promise.reject(error);
        }
    }

    // 玩家確認監聽
    onPlayerConfirmation(playerId, callback) {
        const eventName = `playerConfirmation_${playerId}`;

        const handler = (event) => {
            if (event.detail && event.detail.playerId === playerId) {
                callback();
                window.removeEventListener(eventName, handler);
            }
        };

        window.addEventListener(eventName, handler);

        // 設置超時清理
        setTimeout(() => {
            window.removeEventListener(eventName, handler);
        }, 15000);
    }

    // 獲取對手信息
    getOpponentInfo(match, currentPlayer) {
        const opponent = match.players.find(player => player.id !== currentPlayer.id);

        if (!opponent) {
            return null;
        }

        return {
            id: opponent.id,
            username: opponent.username || '匿名玩家',
            avatar: opponent.avatar || '/assets/images/default-avatar.png',
            level: opponent.level || 1,
            rating: opponent.rating || 1000
        };
    }

    // 創建遊戲房間
    async createGameRoom(gameInstance) {
        try {
            const roomData = {
                roomId: gameInstance.id,
                matchId: gameInstance.matchId,
                players: gameInstance.players,
                settings: gameInstance.settings,
                createdAt: Date.now()
            };

            // 如果有WebSocket服務器，創建房間
            if (window.gameWebSocket && window.gameWebSocket.readyState === WebSocket.OPEN) {
                window.gameWebSocket.send(JSON.stringify({
                    type: 'create_game_room',
                    data: roomData
                }));
            }

            console.log(`🏠 創建遊戲房間: ${gameInstance.id}`);
            return roomData;
        } catch (error) {
            console.error('❌ 創建遊戲房間失敗:', error);
            throw error;
        }
    }

    // 清理過期隊列
    cleanupExpiredQueues() {
        const now = Date.now();
        const queueTimeout = 60000; // 1分鐘超時

        // 清理匹配隊列
        this.queueManagement.matchQueue = this.queueManagement.matchQueue.filter(item => {
            return (now - item.createdAt) < queueTimeout;
        });

        // 清理戰鬥隊列
        this.queueManagement.battleQueue = this.queueManagement.battleQueue.filter(item => {
            return (now - item.createdAt) < queueTimeout;
        });
    }

    // 清理過期會話
    cleanupExpiredSessions() {
        const now = Date.now();
        const sessionTimeout = 30 * 60 * 1000; // 30分鐘超時

        this.playerSessions.forEach((session, playerId) => {
            if (now - session.lastActivity > sessionTimeout) {
                this.playerSessions.delete(playerId);
                console.log(`🧹 清理過期會話: ${playerId}`);
            }
        });
    }

    // 清理內存洩漏
    cleanupMemoryLeaks() {
        // 強制垃圾回收（如果可用）
        if (window.gc) {
            window.gc();
        }

        // 清理大型對象
        this.cleanupLargeObjects();

        // 更新內存使用統計
        this.serverLoad.memory = this.getMemoryUsage();
    }

    // 清理大型對象
    cleanupLargeObjects() {
        // 清理超過限制的活躍遊戲
        if (this.activeGames.size > this.maxConcurrentGames * 1.2) {
            const gamesToRemove = Array.from(this.activeGames.entries())
                .sort((a, b) => a[1].createdAt - b[1].createdAt)
                .slice(0, Math.floor(this.activeGames.size * 0.1));

            gamesToRemove.forEach(([gameId]) => {
                this.cleanupMatch(gameId);
            });
        }
    }

    // 清理非活躍連接
    cleanupInactiveConnections() {
        const now = Date.now();
        const inactiveTimeout = 5 * 60 * 1000; // 5分鐘無活動

        this.playerSessions.forEach((session, playerId) => {
            if (now - session.lastActivity > inactiveTimeout) {
                this.playerSessions.delete(playerId);
                console.log(`🧹 清理非活躍連接: ${playerId}`);
            }
        });
    }

    // 增加匹配超時
    increaseMatchTimeout() {
        // 實現匹配超時增加邏輯
        console.log('⏰ 增加匹配超時時間');
    }

    // 強制資源清理
    forceResourceCleanup() {
        this.cleanupCompletedGames();
        this.cleanupExpiredSessions();
        this.cleanupMemoryLeaks();
        this.cleanupInactiveConnections();

        console.log('🧹 強制資源清理完成');
    }

    // 通知管理員
    notifyAdministrators(message) {
        console.warn('🚨 管理員通知:', message);

        // 可以在這裡添加實際的管理員通知邏輯
        // 例如發送郵件、Slack通知等
    }
}

// 創建全局實例（避免重複創建）
if (!window.concurrencyManager) {
    window.concurrencyManager = new ConcurrencyManager();
}

// 添加全局調試命令
window.debugConcurrency = {
    status: () => window.concurrencyManager.getSystemStatus(),
    games: () => Array.from(window.concurrencyManager.activeGames.entries()),
    load: () => window.concurrencyManager.serverLoad,
    cleanup: () => window.concurrencyManager.cleanupCompletedGames()
};

// 導出供其他模組使用
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ConcurrencyManager;
}
