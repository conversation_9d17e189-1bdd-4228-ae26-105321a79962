// P1P2方塊大亂鬥 - 遊戲匹配系統
if (typeof GameMatchSystem === 'undefined') {
class GameMatchSystem {
    constructor() {
        this.isMatching = false;
        this.matchStartTime = null;
        this.matchTimeout = 60000; // 60秒匹配超時
        this.socket = null;
        this.currentMatch = null;
        this.isEnteringGame = false; // 防止重複進入遊戲

        this.initializeWebSocket();
        this.setupStabilityIntegration();
    }

    // 整合穩定性系統
    setupStabilityIntegration() {
        // 註冊WebSocket連接到連接管理器
        if (window.connectionManager && this.socket) {
            window.connectionManager.registerConnection('gameMatch', this.socket, {
                autoReconnect: true,
                maxReconnectAttempts: 5,
                reconnectDelay: 3000
            });

            // 註冊重連策略
            window.connectionManager.registerReconnectStrategy('gameMatch', (connection) => {
                console.log('🔄 執行遊戲匹配重連策略');
                this.initializeWebSocket();
            });
        }

        // 監聽遊戲狀態變化
        window.addEventListener('gameStateChange', (event) => {
            const { newState, data } = event.detail;
            console.log(`🎮 遊戲狀態變更: ${newState}`, data);
        });

        // 監聽連接降級事件
        window.addEventListener('connectionFallback', (event) => {
            if (event.detail.connection === 'gameMatch') {
                this.handleConnectionFallback();
            }
        });
    }

    // 初始化WebSocket連接
    initializeWebSocket() {
        try {
            const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
            const wsUrl = `${protocol}//${window.location.host}/ws`;

            this.socket = new WebSocket(wsUrl);

            this.socket.onopen = () => {
                console.log('🔗 WebSocket連接已建立');
                this.authenticateSocket();
            };

            this.socket.onmessage = (event) => {
                this.handleSocketMessage(JSON.parse(event.data));
            };

            this.socket.onclose = (event) => {
                console.log('🔌 WebSocket連接已關閉:', {
                    code: event.code,
                    reason: event.reason,
                    wasClean: event.wasClean
                });

                // 在遊戲過程中不要重連，避免干擾遊戲
                if (!this.isMatching && !this.currentMatch) {
                    setTimeout(() => this.initializeWebSocket(), 3000); // 3秒後重連
                } else {
                    console.log('🎮 遊戲過程中，不重連 WebSocket');
                }
            };

            this.socket.onerror = (error) => {
                console.error('❌ WebSocket錯誤:', error);
            };
        } catch (error) {
            console.error('❌ WebSocket初始化失敗:', error);
        }
    }

    // WebSocket認證
    authenticateSocket() {
        const token = localStorage.getItem('token');
        if (token && this.socket.readyState === WebSocket.OPEN) {
            this.socket.send(JSON.stringify({
                type: 'auth',
                token: token
            }));
        }
    }

    // 處理連接降級
    handleConnectionFallback() {
        console.log('📡 遊戲匹配連接進入降級模式');

        // 停止當前匹配
        if (this.isMatching) {
            this.cancelMatch('connection_lost');
            showNotification('網絡連接中斷，匹配已停止', 'warning');
        }

        // 更新狀態到離線模式
        if (window.gameStateManager) {
            window.gameStateManager.changeState('error', {
                reason: 'connection_lost',
                timestamp: Date.now()
            });
        }

        // 顯示離線提示
        this.showOfflineMode();
    }

    // 顯示離線模式
    showOfflineMode() {
        const offlineNotice = document.createElement('div');
        offlineNotice.className = 'offline-notice';
        offlineNotice.innerHTML = `
            <div class="offline-content">
                <i class="fas fa-wifi-slash"></i>
                <h3>離線模式</h3>
                <p>網絡連接中斷，部分功能可能無法使用</p>
                <button class="btn btn-primary" onclick="location.reload()">重新連接</button>
            </div>
        `;

        offlineNotice.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 10000;
        `;

        document.body.appendChild(offlineNotice);
    }

    // 處理WebSocket消息
    handleSocketMessage(message) {
        console.log('📨 收到WebSocket消息:', message);

        switch (message.type) {
            case 'auth_success':
                console.log('✅ WebSocket認證成功');
                break;

            case 'match_found':
                this.handleMatchFound(message.data);
                break;

            case 'match_cancelled':
                this.handleMatchCancelled(message.data);
                break;

            case 'game_start':
                this.handleGameStart(message.data);
                break;

            case 'match_error':
                this.handleMatchError(message.data);
                break;

            case 'block_selection':
                this.handleBlockSelection(message.data);
                break;

            case 'opponent_selection':
                this.handleOpponentSelection(message.data);
                break;

            case 'battle_start':
                this.handleBattleStart(message.data);
                break;

            default:
                console.log('🤷 未知消息類型:', message.type);
        }
    }

    // 開始快速匹配
    async startQuickMatch() {
        if (this.isMatching) {
            console.log('⚠️ 已在匹配中');
            return;
        }

        const user = JSON.parse(localStorage.getItem('user') || '{}');
        if (user.isGuest) {
            showNotification('請先登入以使用匹配功能', 'warning');
            return;
        }

        this.isMatching = true;
        this.matchStartTime = Date.now();

        console.log('🎮 開始快速匹配...');

        // 顯示匹配界面
        this.showMatchingUI();

        // 發送匹配請求
        if (this.socket && this.socket.readyState === WebSocket.OPEN) {
            this.socket.send(JSON.stringify({
                type: 'quick_match',
                data: {
                    gameMode: 'classic_duel',
                    preferences: {
                        element: null, // 隨機元素
                        arena: 'standard'
                    }
                }
            }));
        } else {
            // WebSocket未連接，使用HTTP API作為備用
            this.startMatchViaAPI();
        }

        // 設置匹配超時
        setTimeout(() => {
            if (this.isMatching) {
                this.cancelMatch('timeout');
            }
        }, this.matchTimeout);
    }

    // 通過API開始匹配（備用方案）
    async startMatchViaAPI() {
        try {
            const response = await fetch('/api/game/quick-match', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${localStorage.getItem('token')}`
                },
                body: JSON.stringify({
                    gameMode: 'classic_duel',
                    preferences: {
                        element: null,
                        arena: 'standard'
                    }
                })
            });

            const result = await response.json();

            if (result.success) {
                console.log('✅ 匹配請求已發送');
                // 開始輪詢匹配狀態
                this.pollMatchStatus();
            } else {
                throw new Error(result.message || '匹配請求失敗');
            }
        } catch (error) {
            console.error('❌ 匹配請求失敗:', error);
            this.cancelMatch('error');
            showNotification('匹配失敗，請稍後再試', 'error');
        }
    }

    // 輪詢匹配狀態
    async pollMatchStatus() {
        if (!this.isMatching) return;

        try {
            const response = await fetch('/api/game/match-status', {
                headers: {
                    'Authorization': `Bearer ${localStorage.getItem('token')}`
                }
            });

            const result = await response.json();

            if (result.success) {
                if (result.data.status === 'found') {
                    this.handleMatchFound(result.data);
                } else if (result.data.status === 'searching') {
                    // 繼續輪詢
                    setTimeout(() => this.pollMatchStatus(), 2000);
                }
            }
        } catch (error) {
            console.error('❌ 匹配狀態查詢失敗:', error);
        }
    }

    // 取消匹配
    cancelMatch(reason = 'user') {
        if (!this.isMatching) return;

        console.log('🚫 取消匹配:', reason);

        this.isMatching = false;
        this.matchStartTime = null;

        // 發送取消請求
        if (this.socket && this.socket.readyState === WebSocket.OPEN) {
            this.socket.send(JSON.stringify({
                type: 'cancel_match',
                data: { reason }
            }));
        }

        // 隱藏匹配界面
        this.hideMatchingUI();

        if (reason === 'timeout') {
            showNotification('匹配超時，請稍後再試', 'warning');
        } else if (reason === 'error') {
            showNotification('匹配過程中發生錯誤', 'error');
        }
    }

    // 處理找到匹配
    handleMatchFound(data) {
        console.log('🎉 找到匹配!', data);

        this.currentMatch = data;
        this.isMatching = false;

        // 更新匹配界面
        this.showMatchFoundUI(data);

        // 1秒後進入遊戲（加快進入速度）
        setTimeout(() => {
            this.enterGame(data);
        }, 1000);
    }

    // 處理匹配取消
    handleMatchCancelled(data) {
        console.log('🚫 匹配已取消:', data);
        this.cancelMatch('cancelled');
    }

    // 處理遊戲開始
    handleGameStart(data) {
        console.log('🚀 遊戲開始!', data);
        console.log('🔍 檢查blockSelection對象:', typeof blockSelection, blockSelection);
        console.log('🔗 WebSocket 連接狀態:', this.socket?.readyState);

        // 防止重複處理
        if (this.currentMatch && this.currentMatch.matchId === data.matchId) {
            console.log('⚠️ 遊戲已經開始，跳過重複處理');
            return;
        }

        // 記錄當前匹配
        this.currentMatch = data;
        this.isMatching = false;

        // 關閉匹配界面
        this.hideMatchingUI();

        this.enterGame(data);
    }

    // 處理匹配錯誤
    handleMatchError(data) {
        console.error('❌ 匹配錯誤:', data);
        this.cancelMatch('error');
        showNotification(data.message || '匹配失敗', 'error');
    }

    // 進入遊戲
    enterGame(matchData) {
        console.log('🎮 進入遊戲:', matchData);

        // 防止重複進入
        if (this.isEnteringGame) {
            console.log('⚠️ 正在進入遊戲中，跳過重複調用');
            return;
        }

        this.isEnteringGame = true;

        // 存儲匹配數據
        sessionStorage.setItem('currentMatch', JSON.stringify(matchData));

        // 檢查並開始方塊選擇階段
        if (typeof blockSelection !== 'undefined' && blockSelection) {
            console.log('✅ 找到blockSelection對象，開始方塊選擇');
            blockSelection.startBlockSelection(matchData);

            // 重置進入遊戲標記
            setTimeout(() => {
                this.isEnteringGame = false;
            }, 1000);
        } else {
            console.error('❌ blockSelection對象未找到，嘗試延遲調用');
            // 延遲100ms再試
            setTimeout(() => {
                if (typeof blockSelection !== 'undefined' && blockSelection) {
                    console.log('✅ 延遲調用成功，開始方塊選擇');
                    blockSelection.startBlockSelection(matchData);
                } else {
                    console.error('❌ blockSelection對象仍未找到，請檢查文件加載順序');
                    showNotification('方塊選擇系統載入失敗，請重新整理頁面', 'error');
                }

                // 重置進入遊戲標記
                this.isEnteringGame = false;
            }, 100);
        }
    }

    // 處理方塊選擇消息
    handleBlockSelection(data) {
        console.log('🎯 收到方塊選擇消息:', data);
        // 這裡可以處理服務器的回應
    }

    // 處理對手選擇
    handleOpponentSelection(data) {
        console.log('👥 對手已選擇:', data);
        console.log('🔍 檢查 blockSelection 對象:', typeof blockSelection, blockSelection);

        if (blockSelection) {
            console.log('✅ 調用 blockSelection.handleOpponentSelection');
            blockSelection.handleOpponentSelection(data.element);
        } else {
            console.error('❌ blockSelection 對象不存在');
        }
    }

    // 處理戰鬥開始
    handleBattleStart(data) {
        console.log('⚔️ 戰鬥開始!', data);

        // 關閉方塊選擇界面
        if (blockSelection) {
            blockSelection.closeSelection();
        }

        // 跳轉到戰鬥界面
        showNotification('戰鬥開始！', 'success');

        // 這裡可以跳轉到戰鬥頁面或開始戰鬥邏輯
        // window.location.href = '/battle';
    }

    // 顯示匹配界面
    showMatchingUI() {
        const modal = document.createElement('div');
        modal.id = 'matching-modal';
        modal.className = 'modal-overlay';
        modal.innerHTML = `
            <div class="modal matching-modal">
                <div class="modal-header">
                    <h3>🎮 尋找對手中...</h3>
                    <button class="modal-close" onclick="gameMatch.cancelMatch('user')">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="matching-animation">
                        <div class="matching-spinner"></div>
                        <div class="matching-blocks">
                            <div class="block block-1"></div>
                            <div class="block block-2"></div>
                        </div>
                    </div>
                    <div class="matching-info">
                        <p class="matching-status">正在尋找實力相當的對手...</p>
                        <div class="matching-stats">
                            <span class="stat-item">
                                <i class="fas fa-users"></i>
                                <span id="online-count">1,234</span> 在線
                            </span>
                            <span class="stat-item">
                                <i class="fas fa-clock"></i>
                                <span id="match-time">0</span>s
                            </span>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-secondary" onclick="gameMatch.cancelMatch('user')">
                        取消匹配
                    </button>
                </div>
            </div>
        `;

        document.body.appendChild(modal);

        // 開始計時器
        this.startMatchTimer();
    }

    // 顯示找到匹配界面
    showMatchFoundUI(matchData) {
        const modal = document.getElementById('matching-modal');
        if (!modal) return;

        modal.innerHTML = `
            <div class="modal match-found-modal">
                <div class="modal-header">
                    <h3>🎉 找到對手！</h3>
                </div>
                <div class="modal-body">
                    <div class="match-found-animation">
                        <div class="vs-container">
                            <div class="player player-left">
                                <div class="player-avatar">
                                    <img src="${this.getPlayerAvatar('self')}" alt="你">
                                </div>
                                <div class="player-info">
                                    <div class="player-name">${this.getPlayerName('self')}</div>
                                    <div class="player-rating">⭐ ${matchData.players?.self?.rating || 1000}</div>
                                </div>
                            </div>
                            <div class="vs-text">VS</div>
                            <div class="player player-right">
                                <div class="player-avatar">
                                    <img src="${this.getPlayerAvatar('opponent')}" alt="對手">
                                </div>
                                <div class="player-info">
                                    <div class="player-name">${matchData.players?.opponent?.name || '對手'}</div>
                                    <div class="player-rating">⭐ ${matchData.players?.opponent?.rating || 1000}</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="match-info">
                        <p class="countdown">遊戲將在 <span id="countdown">3</span> 秒後開始...</p>
                        <div class="match-details">
                            <span class="detail-item">
                                <i class="fas fa-map"></i>
                                ${matchData.arena?.name || '標準競技場'}
                            </span>
                            <span class="detail-item">
                                <i class="fas fa-clock"></i>
                                ${matchData.rules?.timeLimit || 180}秒
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // 開始倒計時
        this.startCountdown();
    }

    // 隱藏匹配界面
    hideMatchingUI() {
        const modal = document.getElementById('matching-modal');
        if (modal) {
            modal.remove();
        }
    }

    // 開始匹配計時器
    startMatchTimer() {
        const updateTimer = () => {
            if (!this.isMatching || !this.matchStartTime) return;

            const elapsed = Math.floor((Date.now() - this.matchStartTime) / 1000);
            const timerElement = document.getElementById('match-time');
            if (timerElement) {
                timerElement.textContent = elapsed;
            }

            setTimeout(updateTimer, 1000);
        };

        updateTimer();
    }

    // 開始倒計時
    startCountdown() {
        let count = 3;
        const countdownElement = document.getElementById('countdown');

        const updateCountdown = () => {
            if (countdownElement) {
                countdownElement.textContent = count;
            }

            if (count > 0) {
                count--;
                setTimeout(updateCountdown, 1000);
            } else {
                // 倒計時結束，開始方塊選擇
                setTimeout(() => {
                    this.enterGame(this.currentMatch);
                }, 500);
            }
        };

        updateCountdown();
    }

    // 獲取玩家頭像
    getPlayerAvatar(type) {
        if (type === 'self') {
            const user = JSON.parse(localStorage.getItem('user') || '{}');
            return getAvatarUrl(user);
        } else if (type === 'opponent' && this.currentMatch) {
            const opponent = this.currentMatch.players?.opponent;
            if (opponent) {
                console.log('🖼️ 獲取對手頭像:', opponent);
                return this.getOpponentAvatarUrl(opponent);
            }
        }
        return getDefaultAvatar();
    }

    // 獲取對手頭像 URL
    getOpponentAvatarUrl(opponent) {
        // 優先順序：Discord頭像 > Twitch頭像 > 一般頭像 > 默認頭像
        if (opponent.discord_avatar) {
            console.log('🎮 使用對手Discord頭像:', opponent.discord_avatar);
            return opponent.discord_avatar;
        }

        if (opponent.twitch_avatar) {
            console.log('🎮 使用對手Twitch頭像:', opponent.twitch_avatar);
            return opponent.twitch_avatar;
        }

        if (opponent.avatar) {
            console.log('🎮 使用對手一般頭像:', opponent.avatar);
            return opponent.avatar;
        }

        console.log('🎮 使用默認頭像');
        return getDefaultAvatar();
    }

    // 獲取玩家名稱
    getPlayerName(type) {
        if (type === 'self') {
            const user = JSON.parse(localStorage.getItem('user') || '{}');
            return user.username || user.email || '玩家';
        }
        return '對手';
    }
}
} // 結束 GameMatchSystem 類定義檢查

// 創建全局匹配系統實例（避免重複創建）
if (typeof gameMatch === 'undefined' && typeof GameMatchSystem !== 'undefined') {
    var gameMatch = new GameMatchSystem();
}
